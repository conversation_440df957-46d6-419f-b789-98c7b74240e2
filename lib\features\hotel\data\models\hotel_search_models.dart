import 'package:equatable/equatable.dart';
import 'hotel_details.dart';

/// Request model for /search/init API
class SearchInitRequest extends Equatable {
  final GeoCode geoCode;
  final String locationId;
  final String currency;
  final String culture;
  final String checkIn;
  final String checkOut;
  final List<RoomRequest> rooms;

  const SearchInitRequest({
    required this.geoCode,
    required this.locationId,
    required this.currency,
    required this.culture,
    required this.checkIn,
    required this.checkOut,
    required this.rooms,
  });

  Map<String, dynamic> toJson() {
    return {
      'geoCode': geoCode.toJson(),
      'locationId': locationId,
      'currency': currency,
      'culture': culture,
      'checkIn': checkIn,
      'checkOut': checkOut,
      'rooms': rooms.map((room) => room.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [geoCode, locationId, currency, culture, checkIn, checkOut, rooms];
}

/// GeoCode model for coordinates
class GeoCode extends Equatable {
  final String lat;
  final String long;

  const GeoCode({
    required this.lat,
    required this.long,
  });

  Map<String, dynamic> toJson() {
    return {
      'lat': lat,
      'long': long,
    };
  }

  @override
  List<Object?> get props => [lat, long];
}

/// Room request model
class RoomRequest extends Equatable {
  final String adults;
  final String? children;

  const RoomRequest({
    required this.adults,
    this.children,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'adults': adults,
    };

    if (children != null && children != '0') {
      data['children'] = children;
    }

    return data;
  }

  @override
  List<Object?> get props => [adults, children];
}

/// Response model for /search/init API
class SearchInitResponse extends Equatable {
  final String? searchKey;
  final List<InventoryInfoList> initialHotels;
  final bool success;
  final String? error;
  final SearchMetadata? metadata;

  const SearchInitResponse({
    this.searchKey,
    this.initialHotels = const [],
    this.success = true,
    this.error,
    this.metadata,
  });

  factory SearchInitResponse.fromJson(Map<String, dynamic> json) {
    try {
      final searchKey = json['searchKey'] as String?;
      final hotelsJson = json['hotels'] as List<dynamic>? ?? [];
      final hotels = hotelsJson
          .map((hotel) => InventoryInfoList.fromJson(hotel as Map<String, dynamic>))
          .toList();
      
      final metadata = json['metadata'] != null 
          ? SearchMetadata.fromJson(json['metadata'] as Map<String, dynamic>)
          : null;

      return SearchInitResponse(
        searchKey: searchKey,
        initialHotels: hotels,
        success: true,
        metadata: metadata,
      );
    } catch (e) {
      return SearchInitResponse(
        success: false,
        error: 'Failed to parse search init response: $e',
      );
    }
  }

  factory SearchInitResponse.error(String error) {
    return SearchInitResponse(
      success: false,
      error: error,
    );
  }

  @override
  List<Object?> get props => [searchKey, initialHotels, success, error, metadata];
}

/// Request model for /search polling API
class SearchPollRequest extends Equatable {
  final String searchKey;

  const SearchPollRequest({
    required this.searchKey,
  });

  Map<String, dynamic> toJson() {
    return {
      'search_key': searchKey,
    };
  }

  @override
  List<Object?> get props => [searchKey];
}

/// Response model for /search polling API
class SearchPollResponse extends Equatable {
  final List<InventoryInfoList> hotels;
  final bool isLastBatch;
  final bool isCompleted;
  final bool success;
  final String? error;
  final SearchMetadata? metadata;
  final FilterData? filterData;

  const SearchPollResponse({
    this.hotels = const [],
    this.isLastBatch = false,
    this.isCompleted = false,
    this.success = true,
    this.error,
    this.metadata,
    this.filterData,
  });

  factory SearchPollResponse.fromJson(Map<String, dynamic> json) {
    try {
      final hotelsJson = json['hotels'] as List<dynamic>? ?? [];
      final hotels = hotelsJson
          .map((hotel) => InventoryInfoList.fromJson(hotel as Map<String, dynamic>))
          .toList();

      final metadata = json['metadata'] != null 
          ? SearchMetadata.fromJson(json['metadata'] as Map<String, dynamic>)
          : null;

      final filterData = json['filterData'] != null 
          ? FilterData.fromJson(json['filterData'] as Map<String, dynamic>)
          : null;

      return SearchPollResponse(
        hotels: hotels,
        isLastBatch: json['is_last_batch'] as bool? ?? json['isLastBatch'] as bool? ?? false,
        isCompleted: json['isCompleted'] as bool? ?? false,
        success: true,
        metadata: metadata,
        filterData: filterData,
      );
    } catch (e) {
      return SearchPollResponse(
        success: false,
        error: 'Failed to parse search poll response: $e',
      );
    }
  }

  factory SearchPollResponse.error(String error) {
    return SearchPollResponse(
      success: false,
      error: error,
    );
  }

  @override
  List<Object?> get props => [hotels, isLastBatch, isCompleted, success, error, metadata, filterData];
}

/// Search metadata model
class SearchMetadata extends Equatable {
  final int totalResults;
  final int currentBatch;
  final int totalBatches;
  final String? searchId;

  const SearchMetadata({
    required this.totalResults,
    required this.currentBatch,
    required this.totalBatches,
    this.searchId,
  });

  factory SearchMetadata.fromJson(Map<String, dynamic> json) {
    return SearchMetadata(
      totalResults: json['totalResults'] as int? ?? 0,
      currentBatch: json['currentBatch'] as int? ?? 0,
      totalBatches: json['totalBatches'] as int? ?? 0,
      searchId: json['searchId'] as String?,
    );
  }

  @override
  List<Object?> get props => [totalResults, currentBatch, totalBatches, searchId];
}

/// Filter data model for aggregated filter options
class FilterData extends Equatable {
  final PriceRange? priceRange;
  final List<String> amenities;
  final List<String> hotelTypes;
  final List<double> ratings;
  final List<String> locations;

  const FilterData({
    this.priceRange,
    this.amenities = const [],
    this.hotelTypes = const [],
    this.ratings = const [],
    this.locations = const [],
  });

  factory FilterData.fromJson(Map<String, dynamic> json) {
    return FilterData(
      priceRange: json['priceRange'] != null 
          ? PriceRange.fromJson(json['priceRange'] as Map<String, dynamic>)
          : null,
      amenities: (json['amenities'] as List<dynamic>?)?.cast<String>() ?? [],
      hotelTypes: (json['hotelTypes'] as List<dynamic>?)?.cast<String>() ?? [],
      ratings: (json['ratings'] as List<dynamic>?)?.map((e) => (e as num).toDouble()).toList() ?? [],
      locations: (json['locations'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  @override
  List<Object?> get props => [priceRange, amenities, hotelTypes, ratings, locations];
}

/// Price range model
class PriceRange extends Equatable {
  final double min;
  final double max;

  const PriceRange({
    required this.min,
    required this.max,
  });

  factory PriceRange.fromJson(Map<String, dynamic> json) {
    return PriceRange(
      min: (json['min'] as num?)?.toDouble() ?? 0.0,
      max: (json['max'] as num?)?.toDouble() ?? 0.0,
    );
  }

  @override
  List<Object?> get props => [min, max];
}
