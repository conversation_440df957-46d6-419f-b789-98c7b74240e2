import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/shared/widgets/safe_consumer_widget.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/shared/widgets/custombutton_widget.dart';
import 'package:kind_ali/features/onboarding/presentation/pages/progressive_onboarding_screen.dart';

class LoginPage extends SafeConsumerStatefulWidget {
  final String loginSource;
  const LoginPage({super.key, this.loginSource = 'default'});

  @override
  SafeConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends SafeConsumerState<LoginPage> {

    // ADD THESE NEW VARIABLES:
  final ScrollController _scrollController = ScrollController();
  final FocusNode _otpFocusNode = FocusNode();

  // Helper method for safe translations
  String _tr(String key) => safeTr(key);
  final _formKey = GlobalKey<FormState>();
  final _inputController = TextEditingController();
  final _otpController = TextEditingController();

  // Selection state
  bool isWhatsAppSelected = true; // Default to WhatsApp
  String selectedCountryCode = '+91'; // Default country code

  // OTP state
  bool _showOTPSection = false;
  String? _sentToInput; // Store the input (email/phone) where OTP was sent

  // Country codes list
  final List<Map<String, String>> countryCodes = [
    {'code': '+1', 'country': 'US', 'flag': '🇺🇸'},
    {'code': '+44', 'country': 'UK', 'flag': '🇬🇧'},
    {'code': '+91', 'country': 'IN', 'flag': '🇮🇳'},
    {'code': '+971', 'country': 'UAE', 'flag': '🇦🇪'},
    {'code': '+966', 'country': 'SA', 'flag': '🇸🇦'},
    {'code': '+974', 'country': 'QA', 'flag': '🇶🇦'},
    {'code': '+965', 'country': 'KW', 'flag': '🇰🇼'},
    {'code': '+973', 'country': 'BH', 'flag': '🇧🇭'},
    {'code': '+968', 'country': 'OM', 'flag': '🇴🇲'},
    {'code': '+20', 'country': 'EG', 'flag': '🇪🇬'},
  ];

  @override
  void initState() {
    super.initState();
    // Add listener to OTP focus node for better keyboard handling
    _otpFocusNode.addListener(() {
      // Focus listener for OTP field
    });
  }

  @override
  void dispose() {
    _inputController.dispose();
    _otpController.dispose();
    _scrollController.dispose();
    _otpFocusNode.dispose();
    super.dispose();
  }



  // Send OTP method using AuthProvider
  Future<void> _sendOTP() async {
    if (!_formKey.currentState!.validate()) return;

    String fullInput = isWhatsAppSelected
        ? '$selectedCountryCode${_inputController.text}'
        : _inputController.text;

    try {
      // For demo purposes, simulate OTP sending with potential failure
      await Future.delayed(const Duration(seconds: 1));

      // Simulate success (in real app, this would be an API call)
      bool success = true; // Could be false in real scenarios

      if (success && mounted) {
        safeSetState(() {
          _showOTPSection = true;
          _sentToInput = fullInput;
        });

        Future.delayed(const Duration(milliseconds: 500), () {
          _otpFocusNode.requestFocus();
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('OTP sent to $fullInput'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send OTP. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending OTP: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Verify OTP method using AuthProvider
  Future<void> _verifyOTP() async {
    if (_otpController.text.length != 4) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please enter a valid 4-digit OTP'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final authNotifier = ref.read(authProvider.notifier);

    // Call the appropriate login method based on the login type
    if (isWhatsAppSelected) {
      // Phone/WhatsApp login
      await authNotifier.loginWithPhone(_sentToInput!, _otpController.text);
    } else {
      // Email login
      await authNotifier.loginWithEmail(_sentToInput!, _otpController.text);
    }
    bool success = ref.read(authProvider).error == null;

    if (success && mounted) {
      // Determine navigation route based on profile completion status
      final navigationRoute = await authNotifier.getNavigationRoute(loginSource: widget.loginSource);
      if (mounted) {
        if (navigationRoute == '/booking-continue' && widget.loginSource == 'booking') {
          Navigator.of(context).pop();
        } else if (navigationRoute == '/progressive-onboarding') {
          if (widget.loginSource == 'booking') {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => ProgressiveOnboardingScreen(
                  source: widget.loginSource,
                ),
              ),
            );
          } else {
            Navigator.pushReplacementNamed(context, '/progressive-onboarding', arguments: {'source': widget.loginSource});
          }
        } else {
          Navigator.of(context).pushReplacementNamed('/dashboard');
        }
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Login successful!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(ref.read(authProvider).error ?? 'OTP verification failed. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Handle Google login
  Future<void> _handleGoogleLogin() async {
    final authNotifier = ref.read(authProvider.notifier);

    // For demo purposes, simulate Google login with email
    const demoEmail = '<EMAIL>';
    await authNotifier.loginWithEmail(demoEmail, '1234'); // Use demo OTP for Google login
    bool success = ref.read(authProvider).error == null;

    if (success && mounted) {
      // Determine navigation route based on profile completion status
      final navigationRoute = await authNotifier.getNavigationRoute(loginSource: widget.loginSource);
      if (mounted) {
        if (navigationRoute == '/booking-continue' && widget.loginSource == 'booking') {
          Navigator.of(context).pop();
        } else if (navigationRoute == '/progressive-onboarding') {
          Navigator.pushReplacementNamed(context, '/progressive-onboarding', arguments: {'source': widget.loginSource, 'onBookingContinue': null});
        } else {
          Navigator.of(context).pushReplacementNamed('/dashboard');
        }
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google login successful!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Google login failed. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.primary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Login',
          style: AppTextStyles.headline2.copyWith(
            color: AppColors.text,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Header section with logo and title
          Container(
            padding: EdgeInsets.all(screenHeight < 600 ? 16 : 24),
            child: Column(
              children: [
                // Logo and app name
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(screenHeight < 600 ? 8 : 12),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withAlpha(26),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Image.asset(
                        AppImages.logo,
                        height: screenHeight < 600 ? 24 : 32,
                        width: screenHeight < 600 ? 24 : 32,
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'kind_ali Travel',
                      style: AppTextStyles.headline2.copyWith(
                        color: AppColors.primary,
                        fontSize: screenHeight < 600 ? 16 : 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: screenHeight < 600 ? 16 : 24),

                // Welcome text - hide when keyboard is visible to save space
                if (keyboardHeight == 0) ...[
                  // Text(
                  //   _tr('auth.welcomeBack'),
                  //   style: AppTextStyles.headline1.copyWith(
                  //     color: AppColors.text,
                  //     fontSize: screenHeight < 600 ? 20 : 24,
                  //     fontWeight: FontWeight.bold,
                  //   ),
                  // ),
                  const SizedBox(height: 8),
                  Container(
                    width: 40,
                    height: 3,
                    decoration: BoxDecoration(
                      color: AppColors.accent,
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _tr('auth.signInToContinue'),
                    style: TextStyle(
                      color: AppColors.textLight,
                      fontSize: screenHeight < 600 ? 14 : 16,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Form content
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              padding: EdgeInsets.only(
                left: screenWidth < 400 ? 16 : 24,
                right: screenWidth < 400 ? 16 : 24,
                bottom: 20,
                top: 10,
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                      // Show either input section or OTP section
                      if (!_showOTPSection) ...[
                        // Selection checkboxes
                        Row(
                          children: [
                            // WhatsApp selection
                            GestureDetector(
                              onTap: () {
                                safeSetState(() {
                                  isWhatsAppSelected = true;
                                  _inputController.clear();
                                });
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: isWhatsAppSelected
                                            ? AppColors.primary
                                            : AppColors.textLight,
                                        width: 2,
                                      ),
                                      color: isWhatsAppSelected
                                          ? AppColors.primary
                                          : Colors.transparent,
                                    ),
                                    child: isWhatsAppSelected
                                        ? const Icon(
                                            Icons.check,
                                            size: 14,
                                            color: Colors.white,
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    _tr('auth.whatsapp'),
                                    style: TextStyle(
                                      color: isWhatsAppSelected
                                          ? AppColors.primary
                                          : AppColors.textLight,
                                      fontWeight: isWhatsAppSelected
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ],
                              ),
                            ),
              
                            const SizedBox(width: 30),
              
                            // Email selection
                            GestureDetector(
                              onTap: () {
                                safeSetState(() {
                                  isWhatsAppSelected = false;
                                  _inputController.clear();
                                });
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: !isWhatsAppSelected
                                            ? AppColors.primary
                                            : AppColors.textLight,
                                        width: 2,
                                      ),
                                      color: !isWhatsAppSelected
                                          ? AppColors.primary
                                          : Colors.transparent,
                                    ),
                                    child: !isWhatsAppSelected
                                        ? const Icon(
                                            Icons.check,
                                            size: 14,
                                            color: Colors.white,
                                          )
                                        : null,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    _tr('auth.email'),
                                    style: TextStyle(
                                      color: !isWhatsAppSelected
                                          ? AppColors.primary
                                          : AppColors.textLight,
                                      fontWeight: !isWhatsAppSelected
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
              
                        const SizedBox(height: 20),
              
                        // Input field with country code for WhatsApp
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.divider),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: isWhatsAppSelected
                              ? Row(
                                  children: [
                                    // Country code selector for WhatsApp
                                    InkWell(
                                      onTap: _showCountryCodePicker,
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(12),
                                        bottomLeft: Radius.circular(12),
                                      ),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 16,
                                        ),
                                        decoration: BoxDecoration(
                                          border: Border(
                                            right: BorderSide(
                                              color: AppColors.divider,
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              countryCodes.firstWhere(
                                                (country) => country['code'] == selectedCountryCode,
                                                orElse: () => countryCodes.first,
                                              )['flag']!,
                                              style: const TextStyle(fontSize: 16),
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              selectedCountryCode,
                                              style: TextStyle(
                                                color: AppColors.text,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            Icon(
                                              Icons.keyboard_arrow_down,
                                              color: AppColors.textLight,
                                              size: 20,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    // WhatsApp number input
                                    Expanded(
                                      child: TextFormField(
                                        controller: _inputController,
                                        keyboardType: TextInputType.phone,
                                        onTap: () {
                                          // Input field tapped
                                        },
                                        decoration: InputDecoration(
                                          labelText: _tr('auth.whatsappNumber'),
                                          labelStyle: TextStyle(color: AppColors.textLight),
                                          hintText: _tr('auth.enterWhatsappNumber'),
                                          prefixIcon: Icon(
                                            Icons.phone_outlined,
                                            color: AppColors.primary,
                                          ),
                                          border: InputBorder.none,
                                          contentPadding: const EdgeInsets.symmetric(
                                            vertical: 16,
                                            horizontal: 16,
                                          ),
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return _tr('auth.pleaseEnterWhatsappNumber');
                                          }
                                          if (value.length < 7) {
                                            return _tr('auth.pleaseEnterValidPhoneNumber');
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                  ],
                                )
                              :
                              // Email input field
                              TextFormField(
                                controller: _inputController,
                                keyboardType: TextInputType.emailAddress,
                                onTap: () {
                                  // Email field tapped
                                },
                                decoration: InputDecoration(
                                  labelText: _tr('auth.email'),
                                  labelStyle: TextStyle(color: AppColors.textLight),
                                  hintText: _tr('auth.enterEmailAddress'),
                                  prefixIcon: Icon(
                                    Icons.email_outlined,
                                    color: AppColors.primary,
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                    horizontal: 20,
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return _tr('auth.pleaseEnterEmailAddress');
                                  }
                                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                                    return _tr('auth.pleaseEnterValidEmailAddress');
                                  }
                                  return null;
                                },
                              ),
                        ),
              
                        const SizedBox(height: 24),
              
                        // Continue button
                        CustombuttonWidget(
                          text: _tr('auth.continue'),
                          backgroundColor: AppColors.primary,
                          textColor: Colors.white,
                          borderRadius: 12,
                          height: 56,
                          isFullWidth: true,
                          isLoading: authState.isLoading,
                          onPressed: authState.isLoading ? () {} : _sendOTP,
                          textStyle: AppTextStyles.button.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ] else ...[
                        // OTP Section
                        _buildOTPSection(),
                      ],
              
                      // Only show divider and Google login when not in OTP mode
                      if (!_showOTPSection) ...[
                        const SizedBox(height: 20),
              
                        // Divider
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(width: 100, child: Divider()),
                            Text(
                              ' ${_tr('auth.or')} ',
                              style: TextStyle(
                                color: AppColors.textLight,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 100, child: Divider())
                          ],
                        ),
              
                        const SizedBox(height: 20),
              
                        // Google login button
                        _socialLoginButton(
                          onPressed: authState.isLoading ? () {} : _handleGoogleLogin,
                          icon: Icons.g_mobiledata,
                          label: _tr('auth.continueWithGoogle'),
                          isLoading: authState.isLoading,
                        ),
              
                        const SizedBox(height: 24),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    
  }

  // Build OTP section widget
  Widget _buildOTPSection() {
    final authState = ref.watch(authProvider);
    return Builder(
      builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Back button and title
            Row(
              children: [
                IconButton(
                  onPressed: () {
                    safeSetState(() {
                      _showOTPSection = false;
                      _otpController.clear();
                    });
                  },
                  icon: Icon(
                    Icons.arrow_back,
                    color: AppColors.primary,
                  ),
                ),
                Expanded(
                  child: Text(
                    'Verify OTP',
                    style: AppTextStyles.headline2.copyWith(
                      color: AppColors.text,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48), // Balance the back button
              ],
            ),

            const SizedBox(height: 16),

            // OTP sent message
            Text(
              'We have sent a verification code to',
              style: TextStyle(
                color: AppColors.textLight,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              _sentToInput ?? '',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // OTP input field
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.divider),
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextFormField(
                controller: _otpController,
                focusNode: _otpFocusNode,
                onTap: () {
                  // OTP field tapped
                },
                onChanged: (value) {
                  // OTP input changed
                },
                keyboardType: TextInputType.number,
                textAlign: TextAlign.start,
                maxLength: 4,
                decoration: InputDecoration(
                  labelText: 'Enter 4-digit OTP',
                  labelStyle: TextStyle(color: AppColors.textLight),
                  hintText: '1234',
                  hintStyle: TextStyle(
                    color: AppColors.textLight.withValues(alpha: 0.3),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: AppColors.primary,
                  ),
                  border: InputBorder.none,
                  counterText: '',
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 20,
                  ),
                ),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 8,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Verify button
            CustombuttonWidget(
              text: 'Verify OTP',
              backgroundColor: AppColors.primary,
              textColor: Colors.white,
              borderRadius: 12,
              height: 56,
              isFullWidth: true,
              isLoading: authState.isLoading,
              onPressed: authState.isLoading ? () {} : _verifyOTP,
              textStyle: AppTextStyles.button.copyWith(
                fontSize: 18,
              ),
            ),

            const SizedBox(height: 16),

            // Resend OTP
            TextButton(
              onPressed: authState.isLoading ? null : () {
                _sendOTP(); // Resend OTP
              },
              child: Text(
                'Resend OTP',
                style: TextStyle(
                  color: authState.isLoading ? AppColors.textLight : AppColors.primary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showCountryCodePicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  _tr('auth.selectCountryCode'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text,
                  ),
                ),
              ),

              // Country list
              Expanded(
                child: ListView.builder(
                  itemCount: countryCodes.length,
                  itemBuilder: (context, index) {
                    final country = countryCodes[index];
                    final isSelected = country['code'] == selectedCountryCode;

                    return InkWell(
                      onTap: () {
                        setState(() {
                          selectedCountryCode = country['code']!;
                        });
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppColors.primary.withAlpha(20)
                              : Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Text(
                              country['flag']!,
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              country['country']!,
                              style: TextStyle(
                                color: AppColors.text,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              country['code']!,
                              style: TextStyle(
                                color: isSelected
                                    ? AppColors.primary
                                    : AppColors.textLight,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 8),
                              Icon(
                                Icons.check,
                                color: AppColors.primary,
                                size: 20,
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _socialLoginButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    bool isLoading = false,
  }) {
    return InkWell(
      onTap: isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.divider),
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading)
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              )
            else
              Icon(
                icon,
                size: 24,
                color: AppColors.primary,
              ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: AppColors.text,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
