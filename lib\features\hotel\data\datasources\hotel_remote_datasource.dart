import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:kind_ali/core/constants/api_constants.dart';
import 'package:kind_ali/core/error/exceptions.dart';
import '../models/hotel_details.dart';
import '../models/hotel_search_models.dart';

/// Abstract class for hotel remote data source
abstract class HotelRemoteDataSource {
  /// Get hotels from remote API
  Future<List<InventoryInfoList>> getHotels();
  
  /// Get hotel details by ID from remote API
  Future<InventoryInfoList> getHotelDetails(int hotelId);
  
  /// Search hotels from remote API
  Future<List<InventoryInfoList>> searchHotels({
    required String destination,
    required DateTime checkIn,
    required DateTime checkOut,
    required int guests,
    required int rooms,
  });
  
  /// Get nearby hotels from remote API
  Future<List<InventoryInfoList>> getNearbyHotels({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  });

  /// NEW: Initialize hotel search (Step 1)
  Future<SearchInitResponse> initializeSearch(SearchInitRequest request);

  /// NEW: Poll for hotel search results (Step 2)
  Future<SearchPollResponse> pollSearchResults(SearchPollRequest request);
}

/// Implementation of hotel remote data source
class HotelRemoteDataSourceImpl implements HotelRemoteDataSource {
  final http.Client client;
  
  HotelRemoteDataSourceImpl({required this.client});

  @override
  Future<List<InventoryInfoList>> getHotels() async {
    // TODO: Implement when hotel API endpoints are available
    // Placeholder implementation - returns empty list for now
    return [];

    /* Original implementation - commented out until API endpoints exist
    try {
      final response = await client.get(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.hotels}'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final Hotel hotel = Hotel.fromJson(jsonData);
        return hotel.data?.result?.inventoryInfoList ?? [];
      } else {
        throw ServerException('Failed to fetch hotels: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
    */
  }

  @override
  Future<InventoryInfoList> getHotelDetails(int hotelId) async {
    // TODO: Implement when hotel details API endpoint is available
    // Placeholder implementation - throws exception for now
    throw UnimplementedError('Hotel details API not implemented yet');

    /* Original implementation - commented out until API endpoints exist
    try {
      final response = await client.get(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.hotelDetails.replaceAll('{id}', hotelId.toString())}'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return InventoryInfoList.fromJson(jsonData['data']);
      } else {
        throw ServerException('Failed to fetch hotel details: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
    */
  }

  @override
  Future<List<InventoryInfoList>> searchHotels({
    required String destination,
    required DateTime checkIn,
    required DateTime checkOut,
    required int guests,
    required int rooms,
  }) async {
    // TODO: Implement when search hotels API endpoint is available
    // Placeholder implementation - returns empty list for now
    return [];

    /* Original implementation - commented out until API endpoints exist
    try {
      final queryParams = {
        'destination': destination,
        'check_in': checkIn.toIso8601String().split('T')[0],
        'check_out': checkOut.toIso8601String().split('T')[0],
        'guests': guests.toString(),
        'rooms': rooms.toString(),
      };

      final uri = Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.searchHotels}')
          .replace(queryParameters: queryParams);

      final response = await client.get(
        uri,
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final Hotel hotel = Hotel.fromJson(jsonData);
        return hotel.data?.result?.inventoryInfoList ?? [];
      } else {
        throw ServerException('Failed to search hotels: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
    */
  }

  @override
  Future<List<InventoryInfoList>> getNearbyHotels({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) async {
    // TODO: Implement when nearby hotels API endpoint is available
    // Placeholder implementation - returns empty list for now
    return [];

    /* Original implementation - commented out until API endpoints exist
    try {
      final queryParams = {
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'radius': radiusKm.toString(),
      };

      final uri = Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.nearbyHotels}')
          .replace(queryParameters: queryParams);

      final response = await client.get(
        uri,
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final Hotel hotel = Hotel.fromJson(jsonData);
        return hotel.data?.result?.inventoryInfoList ?? [];
      } else {
        throw ServerException('Failed to fetch nearby hotels: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
    */
  }

  @override
  Future<SearchInitResponse> initializeSearch(SearchInitRequest request) async {
    try {
      print('🔍 Hotel Search Init: ${ApiConstants.fullBaseUrl}${ApiConstants.searchInit}');
      print('📤 Request: ${request.toJson()}');

      final response = await client.post(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.searchInit}'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
        body: json.encode(request.toJson()),
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      print('📡 Search Init Response: Status ${response.statusCode}');
      print('📄 Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final result = SearchInitResponse.fromJson(jsonData);

        if (result.searchKey == null || result.searchKey!.isEmpty) {
          print('❌ Search Init Failed: No searchKey in response');
          return SearchInitResponse.error('Search initialization failed - no search key received');
        }

        print('✅ Search Init Success: searchKey=${result.searchKey}, initialHotels=${result.initialHotels.length}');
        return result;
      } else {
        print('❌ Search Init Error: Status ${response.statusCode}');
        return SearchInitResponse.error('Search initialization failed: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Search Init Exception: $e');
      if (e is ServerException) rethrow;
      return SearchInitResponse.error('Network error during search initialization: $e');
    }
  }

  @override
  Future<SearchPollResponse> pollSearchResults(SearchPollRequest request) async {
    try {
      print('🔄 Hotel Search Poll: ${ApiConstants.fullBaseUrl}${ApiConstants.search}');
      print('📤 Poll Request: ${request.toJson()}');

      final response = await client.post(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.search}'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
        body: json.encode(request.toJson()),
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      print('📡 Search Poll Response: Status ${response.statusCode}');
      print('📄 Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final result = SearchPollResponse.fromJson(jsonData);

        print('✅ Search Poll Success: hotels=${result.hotels.length}, isLastBatch=${result.isLastBatch}, isCompleted=${result.isCompleted}');
        return result;
      } else {
        print('❌ Search Poll Error: Status ${response.statusCode}');
        return SearchPollResponse.error('Search polling failed: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Search Poll Exception: $e');
      if (e is ServerException) rethrow;
      return SearchPollResponse.error('Network error during search polling: $e');
    }
  }
}
