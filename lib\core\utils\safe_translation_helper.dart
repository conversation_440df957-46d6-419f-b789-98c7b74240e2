import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_localizations.dart';

/// Safe translation extension that requires BuildContext parameter
/// This eliminates the global context anti-pattern and prevents widget lifecycle errors
extension SafeStringTranslation on String {
  
  /// Basic translation with context safety check
  /// Usage: 'some.key'.tr(context)
  String tr(BuildContext context) {
    // Safety check: ensure context is still mounted
    if (!context.mounted) {
      debugPrint('Translation attempted on unmounted context for key: $this');
      return '** $this **';
    }
    
    try {
      return AppLocalizations.of(context)?.translate(this) ?? '** $this **';
    } catch (e) {
      debugPrint('Translation error for key: $this, Error: $e');
      return '** $this **';
    }
  }
  
  /// Translation with parameters
  /// Usage: 'greeting'.trParams(context, {'name': 'John'})
  String trParams(BuildContext context, Map<String, String> params) {
    String translation = tr(context);
    
    // Only process parameters if translation was successful
    if (!translation.startsWith('**') && !translation.endsWith('**')) {
      params.forEach((key, value) {
        translation = translation.replaceAll('{$key}', value);
      });
    }
    
    return translation;
  }
  
  /// Plural translation with count
  /// Usage: 'item'.trPlural(context, count)
  String trPlural(BuildContext context, int count) {
    if (!context.mounted) {
      debugPrint('Plural translation attempted on unmounted context for key: $this');
      return '** $this **';
    }
    
    try {
      final localizations = AppLocalizations.of(context);
      if (localizations == null) return '** $this **';
      
      String translationKey;
      if (count == 0) {
        translationKey = '$this.zero';
      } else if (count == 1) {
        translationKey = '$this.one';
      } else {
        translationKey = '$this.other';
      }
      
      // Get the translation and replace the count parameter
      String translation = localizations.translate(translationKey) ?? '** $translationKey **';
      return translation.replaceAll('{count}', count.toString());
    } catch (e) {
      debugPrint('Plural translation error for key: $this, Error: $e');
      return '** $this **';
    }
  }
}

/// Widget-based translation for cases where context passing is difficult
/// Usage: TranslationText('some.key')
class TranslationText extends StatelessWidget {
  final String translationKey;
  final Map<String, String>? params;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  
  const TranslationText(
    this.translationKey, {
    super.key,
    this.params,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });
  
  @override
  Widget build(BuildContext context) {
    String text = params != null 
        ? translationKey.trParams(context, params!)
        : translationKey.tr(context);
    
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Helper class for complex translation scenarios
class SafeTranslationHelper {
  /// Get translation safely with fallback
  static String getTranslation(
    BuildContext context, 
    String key, {
    String? fallback,
    Map<String, String>? params,
  }) {
    if (!context.mounted) {
      return fallback ?? '** $key **';
    }
    
    try {
      String translation = params != null 
          ? key.trParams(context, params)
          : key.tr(context);
      
      // If translation failed and we have a fallback
      if (translation.startsWith('**') && translation.endsWith('**') && fallback != null) {
        return fallback;
      }
      
      return translation;
    } catch (e) {
      debugPrint('SafeTranslationHelper error for key: $key, Error: $e');
      return fallback ?? '** $key **';
    }
  }
  
  /// Get multiple translations at once
  static Map<String, String> getTranslations(
    BuildContext context, 
    List<String> keys,
  ) {
    if (!context.mounted) {
      return Map.fromEntries(keys.map((key) => MapEntry(key, '** $key **')));
    }
    
    return Map.fromEntries(
      keys.map((key) => MapEntry(key, key.tr(context)))
    );
  }
}

/// Extension for easy access to common translations
extension CommonTranslations on BuildContext {
  /// Quick access to common translations
  String get commonCancel => 'common.cancel'.tr(this);
  String get commonOk => 'common.ok'.tr(this);
  String get commonSave => 'common.save'.tr(this);
  String get commonDelete => 'common.delete'.tr(this);
  String get commonEdit => 'common.edit'.tr(this);
  String get commonClose => 'common.close'.tr(this);
  String get commonBack => 'common.back'.tr(this);
  String get commonNext => 'common.next'.tr(this);
  String get commonContinue => 'common.continue'.tr(this);
  String get commonLoading => 'common.loading'.tr(this);
  String get commonError => 'common.error'.tr(this);
  String get commonSuccess => 'common.success'.tr(this);
}
