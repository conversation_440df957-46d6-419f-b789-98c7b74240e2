/// Hotel list state management using Riverpod StateNotifier
library;

import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../hotel/data/models/hotel_details.dart';
import '../../../hotel/data/models/hotel_search_models.dart';
import '../../../hotel/data/datasources/hotel_remote_datasource.dart';
import '../../../home/<USER>/providers/home_notifier.dart';
import 'hotel_providers.dart';

/// Hotel list state class with two-step search support
class HotelListState {
  final String destination;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int guests;
  final bool isLoading;
  final String? error;
  final bool isFilterOpen;
  final String sortOption;
  final List<InventoryInfoList> hotels;
  final List<InventoryInfoList> filteredHotels;

  // NEW: Two-step search properties
  final String? searchKey;
  final bool isSearchInitialized;
  final bool isPolling;
  final bool isSearchCompleted;
  final int currentSkip;
  final SearchMetadata? searchMetadata;
  final FilterData? filterData;
  final bool showShimmerFilters;

  const HotelListState({
    this.destination = '',
    this.checkInDate,
    this.checkOutDate,
    this.guests = 1,
    this.isLoading = false,
    this.error,
    this.isFilterOpen = false,
    this.sortOption = 'popularity',
    this.hotels = const [],
    this.filteredHotels = const [],
    // NEW properties
    this.searchKey,
    this.isSearchInitialized = false,
    this.isPolling = false,
    this.isSearchCompleted = false,
    this.currentSkip = 0,
    this.searchMetadata,
    this.filterData,
    this.showShimmerFilters = true,
  });

  /// Check if has hotels
  bool get hasHotels => filteredHotels.isNotEmpty;

  HotelListState copyWith({
    String? destination,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? guests,
    bool? isLoading,
    String? error,
    bool? isFilterOpen,
    String? sortOption,
    List<InventoryInfoList>? hotels,
    List<InventoryInfoList>? filteredHotels,
    // NEW properties
    String? searchKey,
    bool? isSearchInitialized,
    bool? isPolling,
    bool? isSearchCompleted,
    int? currentSkip,
    SearchMetadata? searchMetadata,
    FilterData? filterData,
    bool? showShimmerFilters,
  }) {
    return HotelListState(
      destination: destination ?? this.destination,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      guests: guests ?? this.guests,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isFilterOpen: isFilterOpen ?? this.isFilterOpen,
      sortOption: sortOption ?? this.sortOption,
      hotels: hotels ?? this.hotels,
      filteredHotels: filteredHotels ?? this.filteredHotels,
      // NEW properties
      searchKey: searchKey ?? this.searchKey,
      isSearchInitialized: isSearchInitialized ?? this.isSearchInitialized,
      isPolling: isPolling ?? this.isPolling,
      isSearchCompleted: isSearchCompleted ?? this.isSearchCompleted,
      currentSkip: currentSkip ?? this.currentSkip,
      searchMetadata: searchMetadata ?? this.searchMetadata,
      filterData: filterData ?? this.filterData,
      showShimmerFilters: showShimmerFilters ?? this.showShimmerFilters,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HotelListState &&
        other.destination == destination &&
        other.checkInDate == checkInDate &&
        other.checkOutDate == checkOutDate &&
        other.guests == guests &&
        other.isLoading == isLoading &&
        other.error == error &&
        other.isFilterOpen == isFilterOpen &&
        other.sortOption == sortOption &&
        other.hotels == hotels &&
        other.filteredHotels == filteredHotels;
  }

  @override
  int get hashCode => Object.hash(
        destination,
        checkInDate,
        checkOutDate,
        guests,
        isLoading,
        error,
        isFilterOpen,
        sortOption,
        hotels,
        filteredHotels,
      );
}

/// Hotel list StateNotifier with two-step search support
class HotelListNotifier extends StateNotifier<HotelListState> {
  final HotelRemoteDataSource _remoteDataSource;
  Timer? _pollingTimer;

  HotelListNotifier(this._remoteDataSource) : super(const HotelListState()) {
    _initializeDefaults();
    // Don't auto-load hotels anymore - wait for search initialization
  }

  @override
  void dispose() {
    _pollingTimer?.cancel();
    super.dispose();
  }

  /// Initialize default values
  void _initializeDefaults() {
    final now = DateTime.now();
    state = state.copyWith(
      checkInDate: now,
      checkOutDate: now.add(const Duration(days: 3)),
      guests: 1,
    );
  }

  /// Set loading state
  void _setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// Load hotels data
  Future<void> loadHotels() async {
    _setLoading(true);
    
    try {
      final String response = await rootBundle.loadString('assets/json/hotels.json');
      final Map<String, dynamic> jsonData = json.decode(response);

      // Navigate to inventoryInfoList
      final List<dynamic>? hotelsData = jsonData['data']?['result']?['inventoryInfoList'];

      if (hotelsData != null) {
        final hotels = hotelsData.map((json) => InventoryInfoList.fromJson(json)).toList();
        state = state.copyWith(
          hotels: hotels,
          error: null,
        );
        _applyFilters();
      } else {
        throw Exception('No hotel data found in JSON');
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load hotels. Please try again.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// NEW: Initialize hotel search (Step 1)
  Future<void> initializeHotelSearch({
    required String destination,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int totalGuests,
    required List<RoomData> roomsData,
    String? locationId,
    double? latitude,
    double? longitude,
  }) async {
    print('🔍 Starting hotel search initialization...');

    // Reset state for new search
    state = state.copyWith(
      destination: destination,
      checkInDate: checkInDate,
      checkOutDate: checkOutDate,
      guests: totalGuests,
      isLoading: true,
      error: null,
      hotels: [],
      filteredHotels: [],
      searchKey: null,
      isSearchInitialized: false,
      isPolling: false,
      isSearchCompleted: false,
      currentSkip: 0,
      searchMetadata: null,
      filterData: null,
      showShimmerFilters: true,
    );

    try {
      // Convert room data to API format
      final apiRooms = roomsData.map((room) => RoomRequest(
        adults: room.adults.toString(),
        children: room.children > 0 ? room.children.toString() : null,
      )).toList();

      // Create geo code (use provided coordinates or default to Dubai)
      final geoCode = GeoCode(
        lat: latitude?.toString() ?? "25.27063",
        long: longitude?.toString() ?? "55.30037",
      );

      final request = SearchInitRequest(
        geoCode: geoCode,
        locationId: locationId ?? "221688", // Default to Dubai if not provided
        currency: "INR",
        culture: "en-us",
        checkIn: checkInDate.toIso8601String().split('T')[0],
        checkOut: checkOutDate.toIso8601String().split('T')[0],
        rooms: apiRooms,
      );

      final response = await _remoteDataSource.initializeSearch(request);

      if (!response.success || response.searchKey == null || response.searchKey!.isEmpty) {
        // Critical check: searchKey missing - navigate back to search page
        print('❌ Search initialization failed: ${response.error}');
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'Search initialization failed. Please try again.',
        );
        // Navigate back to search page immediately
        _navigateBackToSearchPage();
        return;
      }

      print('✅ Search initialized successfully with searchKey: ${response.searchKey}');

      // Update state with search key and initial data
      state = state.copyWith(
        searchKey: response.searchKey,
        isSearchInitialized: true,
        hotels: response.initialHotels,
        searchMetadata: response.metadata,
        showShimmerFilters: response.initialHotels.isNotEmpty,
      );

      // Apply initial filters if we have hotels
      if (response.initialHotels.isNotEmpty) {
        _applyFilters();
      }

      // Start polling immediately
      _startPolling();

    } catch (e) {
      print('❌ Search initialization exception: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize search. Please try again.',
      );
    }
  }

  /// NEW: Start polling for search results (Step 2)
  void _startPolling() {
    if (state.searchKey == null || state.isSearchCompleted) {
      return;
    }

    print('🔄 Starting search polling...');
    state = state.copyWith(isPolling: true);

    _pollingTimer = Timer.periodic(const Duration(seconds: 5), (timer) async {
      await _pollSearchResults();
    });

    // Also poll immediately
    _pollSearchResults();
  }

  /// NEW: Poll for search results
  Future<void> _pollSearchResults() async {
    if (state.searchKey == null || state.isSearchCompleted) {
      _stopPolling();
      return;
    }

    try {
      final request = SearchPollRequest(
        searchKey: state.searchKey!,
      );

      final response = await _remoteDataSource.pollSearchResults(request);

      if (!response.success) {
        print('❌ Search polling failed: ${response.error}');
        return;
      }

      print('📦 Received ${response.hotels.length} new hotels, isLastBatch: ${response.isLastBatch}');

      // Append new hotels to existing list
      final updatedHotels = [...state.hotels, ...response.hotels];

      state = state.copyWith(
        hotels: updatedHotels,
        currentSkip: state.currentSkip + 1,
        searchMetadata: response.metadata ?? state.searchMetadata,
        filterData: response.filterData ?? state.filterData,
        showShimmerFilters: false, // Hide shimmer once we get filter data
        isSearchCompleted: response.isLastBatch || response.isCompleted,
      );

      // Update filtered hotels
      _applyFilters();

      // Stop polling if search is completed
      if (response.isLastBatch || response.isCompleted) {
        print('✅ Search completed!');
        _stopPolling();
        state = state.copyWith(isLoading: false);
      }

    } catch (e) {
      print('❌ Search polling exception: $e');
      // Don't stop polling on individual failures, just log and continue
    }
  }

  /// NEW: Stop polling
  void _stopPolling() {
    _pollingTimer?.cancel();
    _pollingTimer = null;
    state = state.copyWith(isPolling: false);
    print('⏹️ Stopped search polling');
  }

  /// Navigate back to search page when search initialization fails
  void _navigateBackToSearchPage() {
    // This will be handled by the UI layer listening to the error state
    // The UI should check for specific error and navigate back
    print('🔙 Search failed - UI should navigate back to search page');
  }

  /// Apply filters and sorting
  void _applyFilters() {
    List<InventoryInfoList> filtered = List.from(state.hotels);

    // Apply sorting
    switch (state.sortOption) {
      case 'price_low_to_high':
        filtered.sort((a, b) {
          final priceA = a.fareDetail?.displayedBaseFare ?? a.fareDetail?.totalPrice ?? 0.0;
          final priceB = b.fareDetail?.displayedBaseFare ?? b.fareDetail?.totalPrice ?? 0.0;
          return priceA.compareTo(priceB);
        });
        break;
      case 'price_high_to_low':
        filtered.sort((a, b) {
          final priceA = a.fareDetail?.displayedBaseFare ?? a.fareDetail?.totalPrice ?? 0.0;
          final priceB = b.fareDetail?.displayedBaseFare ?? b.fareDetail?.totalPrice ?? 0.0;
          return priceB.compareTo(priceA);
        });
        break;
      case 'rating':
        filtered.sort((a, b) {
          final ratingA = double.tryParse(a.userRating?.toString() ?? '0') ?? a.starRating?.toDouble() ?? 0.0;
          final ratingB = double.tryParse(b.userRating?.toString() ?? '0') ?? b.starRating?.toDouble() ?? 0.0;
          return ratingB.compareTo(ratingA);
        });
        break;
      case 'popularity':
      default:
        // Keep original order for popularity
        break;
    }

    state = state.copyWith(filteredHotels: filtered);
  }

  /// Set destination
  void setDestination(String destination) {
    if (state.destination == destination) return;
    state = state.copyWith(destination: destination);
  }

  /// Set check-in date
  void setCheckInDate(DateTime? date) {
    if (state.checkInDate == date) return;
    
    var newCheckOutDate = state.checkOutDate;
    
    // If check-out date is before check-in date, update it
    if (newCheckOutDate != null && date != null && newCheckOutDate.isBefore(date)) {
      newCheckOutDate = date.add(const Duration(days: 1));
    }

    state = state.copyWith(
      checkInDate: date,
      checkOutDate: newCheckOutDate,
    );
  }

  /// Set check-out date
  void setCheckOutDate(DateTime? date) {
    if (state.checkOutDate == date) return;
    
    var newCheckInDate = state.checkInDate;
    
    // If check-in date is after check-out date, update it
    if (newCheckInDate != null && date != null && newCheckInDate.isAfter(date)) {
      newCheckInDate = date.subtract(const Duration(days: 1));
    }

    state = state.copyWith(
      checkInDate: newCheckInDate,
      checkOutDate: date,
    );
  }

  /// Set guests count
  void setGuests(int guests) {
    if (state.guests == guests) return;
    state = state.copyWith(guests: guests);
  }

  /// Toggle filter panel
  void toggleFilter() {
    state = state.copyWith(isFilterOpen: !state.isFilterOpen);
  }

  /// Set sort option
  void setSortOption(String option) {
    if (state.sortOption == option) return;
    state = state.copyWith(sortOption: option);
    _applyFilters();
  }

  /// Refresh hotels
  Future<void> refreshHotels() async {
    await loadHotels();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Initialize with search parameters
  void initializeWithSearchParams({
    String? destination,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? guests,
  }) {
    state = state.copyWith(
      destination: destination ?? state.destination,
      checkInDate: checkInDate ?? state.checkInDate,
      checkOutDate: checkOutDate ?? state.checkOutDate,
      guests: guests ?? state.guests,
    );
  }
}

/// Hotel list provider with remote data source dependency
final hotelListProvider = StateNotifierProvider<HotelListNotifier, HotelListState>(
  (ref) {
    final remoteDataSource = ref.watch(hotelRemoteDataSourceProvider);
    return HotelListNotifier(remoteDataSource);
  },
);
