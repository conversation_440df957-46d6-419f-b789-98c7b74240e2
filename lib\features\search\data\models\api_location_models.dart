import 'package:equatable/equatable.dart';

/// Response model for autosuggest API
class AutosuggestResponse extends Equatable {
  final List<LocationSuggestion> locationSuggestions;
  final bool success;
  final String? error;

  const AutosuggestResponse({
    required this.locationSuggestions,
    this.success = true,
    this.error,
  });

  factory AutosuggestResponse.fromJson(Map<String, dynamic> json) {
    try {
      // Handle nested structure: {"provider": "...", "data": {"locationSuggestions": [...]}}
      List<dynamic>? suggestionsJson;

      if (json.containsKey('data') && json['data'] is Map<String, dynamic>) {
        // API response has nested structure
        final data = json['data'] as Map<String, dynamic>;
        suggestionsJson = data['locationSuggestions'] as List<dynamic>?;
      } else {
        // Direct structure (fallback)
        suggestionsJson = json['locationSuggestions'] as List<dynamic>?;
      }

      final suggestions = (suggestionsJson)
          ?.map((item) => LocationSuggestion.fromJson(item as Map<String, dynamic>))
          .toList() ?? [];

      return AutosuggestResponse(
        locationSuggestions: suggestions,
        success: true,
      );
    } catch (e) {
      return AutosuggestResponse(
        locationSuggestions: [],
        success: false,
        error: 'Failed to parse autosuggest response: $e',
      );
    }
  }

  factory AutosuggestResponse.error(String error) {
    return AutosuggestResponse(
      locationSuggestions: [],
      success: false,
      error: error,
    );
  }

  @override
  List<Object?> get props => [locationSuggestions, success, error];
}

/// Individual location suggestion from autosuggest API
class LocationSuggestion extends Equatable {
  final String id;
  final String name;
  final String? displayName;
  final LocationCoordinates coordinates;
  final String? type;
  final String? country;
  final String? region;

  const LocationSuggestion({
    required this.id,
    required this.name,
    this.displayName,
    required this.coordinates,
    this.type,
    this.country,
    this.region,
  });

  factory LocationSuggestion.fromJson(Map<String, dynamic> json) {
    return LocationSuggestion(
      id: json['id'].toString(), // Convert to string in case it's a number
      name: json['name'] as String,
      displayName: json['fullName'] as String? ?? json['displayName'] as String?,
      coordinates: LocationCoordinates.fromJson(json['coordinates'] as Map<String, dynamic>),
      type: json['type'] as String?,
      country: json['country'] as String?,
      region: json['state'] as String? ?? json['region'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'coordinates': coordinates.toJson(),
      'type': type,
      'country': country,
      'region': region,
    };
  }

  @override
  List<Object?> get props => [id, name, displayName, coordinates, type, country, region];
}

/// Coordinates model for location
class LocationCoordinates extends Equatable {
  final double latitude;
  final double longitude;

  const LocationCoordinates({
    required this.latitude,
    required this.longitude,
  });

  factory LocationCoordinates.fromJson(Map<String, dynamic> json) {
    return LocationCoordinates(
      latitude: (json['lat'] as num?)?.toDouble() ?? (json['latitude'] as num).toDouble(),
      longitude: (json['long'] as num?)?.toDouble() ?? (json['longitude'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  List<Object?> get props => [latitude, longitude];
}

/// Result model for API-integrated location operations
class ApiLocationResult extends Equatable {
  final bool isSuccess;
  final LocationSuggestion? selectedLocation;
  final String? error;
  final String? displayName;
  final bool isFromCache;

  const ApiLocationResult({
    required this.isSuccess,
    this.selectedLocation,
    this.error,
    this.displayName,
    this.isFromCache = false,
  });

  factory ApiLocationResult.success({
    required LocationSuggestion selectedLocation,
    bool isFromCache = false,
  }) {
    return ApiLocationResult(
      isSuccess: true,
      selectedLocation: selectedLocation,
      displayName: selectedLocation.displayName ?? selectedLocation.name,
      isFromCache: isFromCache,
    );
  }

  factory ApiLocationResult.error(String error) {
    return ApiLocationResult(
      isSuccess: false,
      error: error,
    );
  }

  ApiLocationResult copyWith({
    bool? isSuccess,
    LocationSuggestion? selectedLocation,
    String? error,
    String? displayName,
    bool? isFromCache,
  }) {
    return ApiLocationResult(
      isSuccess: isSuccess ?? this.isSuccess,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      error: error ?? this.error,
      displayName: displayName ?? this.displayName,
      isFromCache: isFromCache ?? this.isFromCache,
    );
  }

  @override
  List<Object?> get props => [isSuccess, selectedLocation, error, displayName, isFromCache];
}

/// Enum for location error types
enum LocationErrorType {
  permissionDenied,
  permissionDeniedForever,
  serviceDisabled,
  apiError,
  networkError,
  geocodingError,
  unknown,
}
