import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/services/location_service.dart';
import 'package:kind_ali/features/search/presentation/providers/search_cities_notifier.dart';
import 'package:kind_ali/features/search/data/models/api_location_models.dart';

/// Smart location state
class SmartLocationState {
  final String displayLocation;
  final SmartLocationSource source;
  final bool isLoading;
  final String? error;
  final bool hasLocationPermission;
  final bool isLocationServiceEnabled;

  const SmartLocationState({
    required this.displayLocation,
    required this.source,
    this.isLoading = false,
    this.error,
    this.hasLocationPermission = false,
    this.isLocationServiceEnabled = false,
  });

  SmartLocationState copyWith({
    String? displayLocation,
    SmartLocationSource? source,
    bool? isLoading,
    String? error,
    bool? hasLocationPermission,
    bool? isLocationServiceEnabled,
  }) {
    return SmartLocationState(
      displayLocation: displayLocation ?? this.displayLocation,
      source: source ?? this.source,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasLocationPermission: hasLocationPermission ?? this.hasLocationPermission,
      isLocationServiceEnabled: isLocationServiceEnabled ?? this.isLocationServiceEnabled,
    );
  }

  /// Check if location needs permission request
  bool needsLocationPermission() {
    return !hasLocationPermission && source != SmartLocationSource.currentLocation;
  }

  /// Get display text with appropriate styling info
  String getDisplayText() {
    switch (source) {
      case SmartLocationSource.currentLocation:
        return displayLocation;
      case SmartLocationSource.recentSearch:
        return displayLocation;
      case SmartLocationSource.popularPlace:
        return displayLocation;
      case SmartLocationSource.fallback:
        return 'Where do you want to stay?';
    }
  }

  /// Get subtitle text for additional context
  String? getSubtitleText() {
    switch (source) {
      case SmartLocationSource.currentLocation:
        return 'Current location';
      case SmartLocationSource.recentSearch:
        return 'Recent search';
      case SmartLocationSource.popularPlace:
        return 'Popular destination';
      case SmartLocationSource.fallback:
        return null;
    }
  }
}

/// Source of the smart location (in priority order)
enum SmartLocationSource {
  recentSearch,    // HIGHEST PRIORITY - User's last searched location
  currentLocation, // SECOND PRIORITY - User's current GPS location
  popularPlace,    // THIRD PRIORITY - Popular destination fallback
  fallback,        // LOWEST PRIORITY - Default placeholder
}

/// Smart location notifier that determines the best location to display
///
/// Priority Order:
/// 1. Recent Search Location (highest priority - user's last searched city)
/// 2. Current Location (GPS location if permissions available)
/// 3. Popular Place (fallback to popular destination)
/// 4. Default Placeholder (final fallback)
class SmartLocationNotifier extends StateNotifier<SmartLocationState> {
  static const String _lastSelectedLocationKey = 'last_selected_location';
  static const String _recentSearchLocationKey = 'recent_search_location';

  SmartLocationNotifier()
      : super(const SmartLocationState(
          displayLocation: 'Where do you want to stay?',
          source: SmartLocationSource.fallback,
        )) {
    _initializeSmartLocation();
  }

  /// Initialize smart location with priority logic
  Future<void> _initializeSmartLocation() async {
    state = state.copyWith(isLoading: true);

    try {
      // Step 1: Check location permission and service status
      final hasPermission = await LocationService.hasLocationPermission();
      final isServiceEnabled = await _isLocationServiceEnabled();

      state = state.copyWith(
        hasLocationPermission: hasPermission,
        isLocationServiceEnabled: isServiceEnabled,
      );

      // Step 2: FIRST PRIORITY - Check for recent search location
      final recentLocation = await _getRecentSearchLocation();
      if (recentLocation != null) {
        state = state.copyWith(
          displayLocation: recentLocation,
          source: SmartLocationSource.recentSearch,
          isLoading: false,
        );
        return;
      }

      // Step 3: SECOND PRIORITY - Try to get current location if permissions are available
      if (hasPermission && isServiceEnabled) {
        await _tryCurrentLocation();
        return;
      }

      // Step 4: THIRD PRIORITY - Fallback to popular place
      final popularPlace = await _getPopularPlace();
      if (popularPlace != null) {
        state = state.copyWith(
          displayLocation: popularPlace,
          source: SmartLocationSource.popularPlace,
          isLoading: false,
        );
        return;
      }

      // Step 5: Final fallback
      state = state.copyWith(
        displayLocation: 'Where do you want to stay?',
        source: SmartLocationSource.fallback,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to determine location: $e',
        isLoading: false,
      );
    }
  }

  /// Try to get current location using new API integration
  Future<void> _tryCurrentLocation() async {
    try {
      final apiLocationResult = await LocationService.getCurrentLocationWithAPI(useCache: true);

      if (apiLocationResult.isSuccess && apiLocationResult.selectedLocation != null) {
        final displayName = apiLocationResult.displayName ??
                           apiLocationResult.selectedLocation!.displayName ??
                           apiLocationResult.selectedLocation!.name;

        state = state.copyWith(
          displayLocation: displayName,
          source: SmartLocationSource.currentLocation,
          isLoading: false,
        );

        // Save as last known location
        await _saveLastSelectedLocation(displayName);
      } else {
        // Current location failed, try fallbacks
        await _tryFallbackLocations();
      }
    } catch (e) {
      await _tryFallbackLocations();
    }
  }

  /// Try fallback locations when current location fails
  Future<void> _tryFallbackLocations() async {
    // FIRST PRIORITY - Try recent search location
    final recentLocation = await _getRecentSearchLocation();
    if (recentLocation != null) {
      state = state.copyWith(
        displayLocation: recentLocation,
        source: SmartLocationSource.recentSearch,
        isLoading: false,
      );
      return;
    }

    // SECOND PRIORITY - Try popular place
    final popularPlace = await _getPopularPlace();
    if (popularPlace != null) {
      state = state.copyWith(
        displayLocation: popularPlace,
        source: SmartLocationSource.popularPlace,
        isLoading: false,
      );
      return;
    }

    // Final fallback
    state = state.copyWith(
      displayLocation: 'Where do you want to stay?',
      source: SmartLocationSource.fallback,
      isLoading: false,
    );
  }

  /// Check if location service is enabled
  Future<bool> _isLocationServiceEnabled() async {
    try {
      final result = await LocationService.getCurrentLocation(useCache: false);
      return result.errorType != LocationErrorType.serviceDisabled;
    } catch (e) {
      return false;
    }
  }

  /// Get recent search location from SharedPreferences
  Future<String?> _getRecentSearchLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_recentSearchLocationKey);
    } catch (e) {
      return null;
    }
  }

  /// Get a popular place as fallback
  Future<String?> _getPopularPlace() async {
    try {
      // You can customize this list based on your app's popular destinations
      const popularPlaces = [
        'Mumbai',
        'Delhi',
        'Bangalore',
        'Chennai',
        'Kolkata',
        'Hyderabad',
        'Pune',
        'Goa',
      ];
      
      // Return the first popular place as fallback
      return popularPlaces.isNotEmpty ? popularPlaces.first : null;
    } catch (e) {
      return null;
    }
  }

  /// Save last selected location
  Future<void> _saveLastSelectedLocation(String location) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSelectedLocationKey, location);
    } catch (e) {
      // Ignore save errors
    }
  }

  /// Save recent search location
  Future<void> saveRecentSearchLocation(String location) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_recentSearchLocationKey, location);
    } catch (e) {
      // Ignore save errors
    }
  }

  /// Request location permission and update location
  Future<void> requestLocationAndUpdate() async {
    state = state.copyWith(isLoading: true);

    try {
      final apiLocationResult = await LocationService.getCurrentLocationWithAPI(useCache: false);

      if (apiLocationResult.isSuccess && apiLocationResult.selectedLocation != null) {
        final displayName = apiLocationResult.displayName ??
                           apiLocationResult.selectedLocation!.displayName ??
                           apiLocationResult.selectedLocation!.name;

        state = state.copyWith(
          displayLocation: displayName,
          source: SmartLocationSource.currentLocation,
          hasLocationPermission: true,
          isLocationServiceEnabled: true,
          isLoading: false,
        );

        await _saveLastSelectedLocation(displayName);
      } else {
        state = state.copyWith(
          error: apiLocationResult.error ?? 'Can\'t fetch current location',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Can\'t fetch current location',
        isLoading: false,
      );
    }
  }

  /// Manually set location (when user selects from search)
  void setLocation(String location, SmartLocationSource source) {
    state = state.copyWith(
      displayLocation: location,
      source: source,
    );
    
    // Save as recent search location if it's from user selection
    if (source == SmartLocationSource.recentSearch) {
      saveRecentSearchLocation(location);
    }
  }

  /// Refresh location (try to get current location again)
  Future<void> refreshLocation() async {
    await _initializeSmartLocation();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }




}

/// Provider for smart location
final smartLocationProvider = StateNotifierProvider<SmartLocationNotifier, SmartLocationState>(
  (ref) => SmartLocationNotifier(),
);
