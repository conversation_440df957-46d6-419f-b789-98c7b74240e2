import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';

/// Shimmer effect for filter panel, sort options, and price range controls
class FilterPanelShimmer extends StatefulWidget {
  const FilterPanelShimmer({Key? key}) : super(key: key);

  @override
  State<FilterPanelShimmer> createState() => _FilterPanelShimmerState();
}

class _FilterPanelShimmerState extends State<FilterPanelShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Filter header row shimmer
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildShimmerBox(
                    height: 20,
                    width: 80,
                  ),
                  _buildShimmerBox(
                    height: 32,
                    width: 32,
                    borderRadius: BorderRadius.circular(16),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Sort options shimmer
              Row(
                children: [
                  _buildShimmerBox(
                    height: 36,
                    width: 100,
                    borderRadius: BorderRadius.circular(18),
                  ),
                  const SizedBox(width: 12),
                  _buildShimmerBox(
                    height: 36,
                    width: 80,
                    borderRadius: BorderRadius.circular(18),
                  ),
                  const SizedBox(width: 12),
                  _buildShimmerBox(
                    height: 36,
                    width: 90,
                    borderRadius: BorderRadius.circular(18),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Price range shimmer
              _buildShimmerBox(
                height: 18,
                width: 100,
              ),
              const SizedBox(height: 12),
              
              // Price range slider shimmer
              Stack(
                children: [
                  _buildShimmerBox(
                    height: 4,
                    width: double.infinity,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  Positioned(
                    left: 60,
                    top: -6,
                    child: _buildShimmerBox(
                      height: 16,
                      width: 16,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  Positioned(
                    right: 80,
                    top: -6,
                    child: _buildShimmerBox(
                      height: 16,
                      width: 16,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Price labels shimmer
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildShimmerBox(
                    height: 14,
                    width: 60,
                  ),
                  _buildShimmerBox(
                    height: 14,
                    width: 60,
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Filter categories shimmer
              _buildFilterCategoryShimmer('Star Rating'),
              const SizedBox(height: 16),
              _buildFilterCategoryShimmer('Amenities'),
              const SizedBox(height: 16),
              _buildFilterCategoryShimmer('Property Type'),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterCategoryShimmer(String category) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildShimmerBox(
          height: 16,
          width: 100,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: List.generate(4, (index) => 
            _buildShimmerBox(
              height: 32,
              width: 60 + (index * 10).toDouble(),
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerBox({
    required double height,
    required double width,
    BorderRadius? borderRadius,
  }) {
    return Stack(
      children: [
        Container(
          height: height,
          width: width,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: borderRadius ?? BorderRadius.circular(4),
          ),
        ),
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: borderRadius ?? BorderRadius.circular(4),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                stops: [
                  _animation.value - 0.3,
                  _animation.value,
                  _animation.value + 0.3,
                ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
                colors: [
                  Colors.transparent,
                  Colors.white.withOpacity(0.6),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Compact shimmer for the top filter bar
class FilterBarShimmer extends StatefulWidget {
  const FilterBarShimmer({Key? key}) : super(key: key);

  @override
  State<FilterBarShimmer> createState() => _FilterBarShimmerState();
}

class _FilterBarShimmerState extends State<FilterBarShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingSizeDefault,
            vertical: AppDimensions.paddingSizeSmall,
          ),
          child: Row(
            children: [
              _buildShimmerBox(
                height: 36,
                width: 80,
                borderRadius: BorderRadius.circular(18),
              ),
              const SizedBox(width: 12),
              _buildShimmerBox(
                height: 36,
                width: 60,
                borderRadius: BorderRadius.circular(18),
              ),
              const Spacer(),
              _buildShimmerBox(
                height: 36,
                width: 100,
                borderRadius: BorderRadius.circular(18),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({
    required double height,
    required double width,
    BorderRadius? borderRadius,
  }) {
    return Stack(
      children: [
        Container(
          height: height,
          width: width,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: borderRadius ?? BorderRadius.circular(4),
          ),
        ),
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: borderRadius ?? BorderRadius.circular(4),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                stops: [
                  _animation.value - 0.3,
                  _animation.value,
                  _animation.value + 0.3,
                ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
                colors: [
                  Colors.transparent,
                  Colors.white.withOpacity(0.6),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
