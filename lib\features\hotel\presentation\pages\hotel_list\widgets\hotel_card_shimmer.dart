import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';

/// Shimmer effect widget that matches the hotel card shape
class HotelCardShimmer extends StatefulWidget {
  const HotelCardShimmer({Key? key}) : super(key: key);

  @override
  State<HotelCardShimmer> createState() => _HotelCardShimmerState();
}

class _HotelCardShimmerState extends State<HotelCardShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double cardHeight = 460.0;
    final double borderRadius = AppDimensions.radiusExtraLarge;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          height: cardHeight,
          margin: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingSizeDefault,
            vertical: AppDimensions.paddingSizeSmall,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(borderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Image shimmer
                    _buildShimmerBox(
                      height: 250,
                      width: double.infinity,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(borderRadius),
                        topRight: Radius.circular(borderRadius),
                      ),
                    ),
                    
                    // Content shimmer
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingSizeDefault),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Hotel name shimmer
                            _buildShimmerBox(
                              height: 20,
                              width: MediaQuery.of(context).size.width * 0.6,
                            ),
                            const SizedBox(height: 8),
                            
                            // Stars shimmer
                            Row(
                              children: List.generate(5, (index) => 
                                Padding(
                                  padding: const EdgeInsets.only(right: 2),
                                  child: _buildShimmerBox(
                                    height: 14,
                                    width: 14,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            
                            // Location shimmer
                            _buildShimmerBox(
                              height: 16,
                              width: MediaQuery.of(context).size.width * 0.4,
                            ),
                            const SizedBox(height: 8),
                            
                            // Description shimmer
                            _buildShimmerBox(
                              height: 14,
                              width: double.infinity,
                            ),
                            const SizedBox(height: 4),
                            _buildShimmerBox(
                              height: 14,
                              width: MediaQuery.of(context).size.width * 0.7,
                            ),
                            
                            const Spacer(),
                            
                            // Price and button row shimmer
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildShimmerBox(
                                      height: 12,
                                      width: 60,
                                    ),
                                    const SizedBox(height: 4),
                                    _buildShimmerBox(
                                      height: 18,
                                      width: 80,
                                    ),
                                  ],
                                ),
                                _buildShimmerBox(
                                  height: 36,
                                  width: 100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Shimmer overlay effect
                Positioned.fill(
                  child: _buildShimmerOverlay(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({
    required double height,
    required double width,
    BorderRadius? borderRadius,
  }) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: borderRadius ?? BorderRadius.circular(4),
      ),
    );
  }

  Widget _buildShimmerOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: [
            _animation.value - 0.3,
            _animation.value,
            _animation.value + 0.3,
          ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
          colors: [
            Colors.transparent,
            Colors.white.withOpacity(0.4),
            Colors.transparent,
          ],
        ),
      ),
    );
  }
}

/// Widget to display multiple hotel card shimmers
class HotelListShimmer extends StatelessWidget {
  final int itemCount;

  const HotelListShimmer({
    Key? key,
    this.itemCount = 3,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) => const HotelCardShimmer(),
    );
  }
}
