import 'dart:async';
import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../../features/search/data/models/api_location_models.dart';
import '../../features/search/data/datasources/search_remote_datasource.dart';

/// Centralized location service with API integration
class LocationService {
  static const String _locationCacheKey = 'api_location_cache';
  static const String _locationCacheTimeKey = 'api_location_cache_time';
  static const int _cacheValidityMinutes = 10; // Cache location for 10 minutes

  static SearchRemoteDataSource? _remoteDataSource;

  /// Initialize the location service with remote data source
  static void initialize(SearchRemoteDataSource remoteDataSource) {
    _remoteDataSource = remoteDataSource;
  }

  /// Get current location with API integration (NEW MANDATORY METHOD)
  static Future<ApiLocationResult> getCurrentLocationWithAPI({
    bool useCache = true,
  }) async {
    debugPrint('🔍 LocationService: Starting getCurrentLocationWithAPI');

    if (_remoteDataSource == null) {
      debugPrint('❌ LocationService: Remote data source not initialized');
      return ApiLocationResult.error('Location service not initialized. Call LocationService.initialize() first.');
    }

    try {
      // Step 1: Check cache first if enabled
      if (useCache) {
        debugPrint('📦 LocationService: Checking cache');
        final cachedResult = await _getCachedApiLocation();
        if (cachedResult != null) {
          debugPrint('✅ LocationService: Using cached location');
          return cachedResult;
        }
      }

      // Step 2: Check if location services are enabled
      debugPrint('🛰️ LocationService: Checking if location services are enabled');
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ LocationService: Location services disabled');
        return ApiLocationResult.error('Can\'t fetch current location - Location services disabled');
      }

      // Step 3: Check and request permissions
      debugPrint('🔐 LocationService: Checking permissions');
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        debugPrint('⚠️ LocationService: Permission denied, requesting...');
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('❌ LocationService: Permission denied after request');
          return ApiLocationResult.error('Can\'t fetch current location - Permission denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('❌ LocationService: Permission denied forever');
        return ApiLocationResult.error('Can\'t fetch current location - Permission denied forever');
      }

      // Step 4: Get current GPS position with fallback accuracy
      debugPrint('📍 LocationService: Getting GPS position with 15s timeout');
      Position position;

      try {
        // Try high accuracy first
        position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 15),
        );
        debugPrint('✅ LocationService: Got high accuracy GPS position: ${position.latitude}, ${position.longitude}');
      } catch (e) {
        debugPrint('⚠️ LocationService: High accuracy GPS failed: $e');
        debugPrint('🔄 LocationService: Trying medium accuracy GPS...');

        try {
          // Fallback to medium accuracy with shorter timeout
          position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.medium,
            timeLimit: const Duration(seconds: 10),
          );
          debugPrint('✅ LocationService: Got medium accuracy GPS position: ${position.latitude}, ${position.longitude}');
        } catch (e2) {
          debugPrint('❌ LocationService: Medium accuracy GPS also failed: $e2');
          debugPrint('🔄 LocationService: Trying last known position...');

          // Last resort: try to get last known position
          position = await Geolocator.getLastKnownPosition() ??
              await Geolocator.getCurrentPosition(
                desiredAccuracy: LocationAccuracy.low,
                timeLimit: const Duration(seconds: 5),
              );
          debugPrint('✅ LocationService: Got fallback GPS position: ${position.latitude}, ${position.longitude}');
        }
      }

      // Step 5: Try reverse geocoding with quick fallback to coordinates
      debugPrint('🗺️ LocationService: Attempting reverse geocoding...');
      String searchTerm;

      try {
        // Try reverse geocoding with a very short timeout for faster fallback
        searchTerm = await _getCityNameFromCoordinates(
          position.latitude,
          position.longitude,
        );
        debugPrint('✅ LocationService: Got search term from reverse geocoding: $searchTerm');
      } catch (e) {
        // Immediate fallback to coordinates if reverse geocoding fails
        debugPrint('⚡ LocationService: Reverse geocoding failed, using coordinates immediately');
        searchTerm = _formatCoordinatesAsFallback(position.latitude, position.longitude);
        debugPrint('✅ LocationService: Using coordinates as search term: $searchTerm');
      }

      // Step 6: Call autosuggest API with search term
      debugPrint('🌐 LocationService: Calling autosuggest API with: $searchTerm');
      final autosuggestResponse = await _remoteDataSource!.autoSuggestLocation(searchTerm);
      debugPrint('📡 LocationService: API response success: ${autosuggestResponse.success}, suggestions count: ${autosuggestResponse.locationSuggestions.length}');

      if (!autosuggestResponse.success || autosuggestResponse.locationSuggestions.isEmpty) {
        debugPrint('❌ LocationService: API failed or no suggestions. Error: ${autosuggestResponse.error}');
        return ApiLocationResult.error('Can\'t fetch current location - API error');
      }

      // Step 7: Auto-select first item from locationSuggestions
      final selectedLocation = autosuggestResponse.locationSuggestions.first;
      debugPrint('✅ LocationService: Selected location from autosuggest: ${selectedLocation.name} (${selectedLocation.id})');

      // Step 8: Store id and coordinates and make fire-and-forget call to /location
      debugPrint('💾 LocationService: Storing id=${selectedLocation.id} and coordinates=${selectedLocation.coordinates.latitude},${selectedLocation.coordinates.longitude}');
      debugPrint('🔥 LocationService: Fire-and-forget call to /location for current location');
      final fireAndForgetSuccess = await _remoteDataSource!.fireAndForgetLocation(
        selectedLocation.id,
        selectedLocation.coordinates,
      );
      debugPrint('📤 LocationService: Fire-and-forget result: $fireAndForgetSuccess (200 OK status verification only)');

      // Step 9: Cache the result
      final result = ApiLocationResult.success(
        selectedLocation: selectedLocation,
        isFromCache: false,
      );
      await _cacheApiLocation(result);
      debugPrint('✅ LocationService: Successfully completed API flow');

      return result;
    } catch (e) {
      debugPrint('❌ LocationService: Error in API flow: $e');

      // Determine error type for better user messaging
      String userFriendlyError;
      if (e.toString().contains('TimeoutException') || e.toString().contains('timeout')) {
        userFriendlyError = 'Location detection timed out. Please try again or search manually.';
      } else if (e.toString().contains('SocketException') || e.toString().contains('network')) {
        userFriendlyError = 'Network error. Please check your connection and try again.';
      } else if (e.toString().contains('Permission') || e.toString().contains('permission')) {
        userFriendlyError = 'Location permission required. Please enable location access.';
      } else {
        userFriendlyError = 'Can\'t detect current location. Please search manually.';
      }

      // Try to return cached location as fallback
      final cachedResult = await _getCachedApiLocation();
      if (cachedResult != null) {
        debugPrint('📦 LocationService: Using cached fallback due to error');
        return cachedResult.copyWith(
          isFromCache: true,
          error: 'Using last known location. $userFriendlyError',
        );
      }

      debugPrint('❌ LocationService: No cached fallback available');
      return ApiLocationResult.error(userFriendlyError);
    }
  }

  /// Handle manual location selection from autosuggest dropdown
  /// This WILL call the fire-and-forget /location API
  static Future<bool> selectLocationFromSuggestion(LocationSuggestion selectedLocation) async {
    if (_remoteDataSource == null) {
      debugPrint('❌ LocationService: Remote data source not initialized for manual selection');
      return false;
    }

    try {
      debugPrint('👆 LocationService: User manually selected: ${selectedLocation.name} (${selectedLocation.id})');

      // Call fire-and-forget /location API for manual selection
      debugPrint('🔥 LocationService: Fire-and-forget call to /location for manual selection');
      final fireAndForgetSuccess = await _remoteDataSource!.fireAndForgetLocation(
        selectedLocation.id,
        selectedLocation.coordinates,
      );
      debugPrint('📤 LocationService: Fire-and-forget result: $fireAndForgetSuccess');

      return fireAndForgetSuccess;
    } catch (e) {
      debugPrint('❌ LocationService: Error in manual selection: $e');
      return false;
    }
  }

  /// Helper method to get city name from coordinates using reverse geocoding with robust fallback
  static Future<String> _getCityNameFromCoordinates(double latitude, double longitude) async {
    try {
      debugPrint('🗺️ LocationService: Starting reverse geocoding with 5s timeout...');

      // Use very short timeout for faster fallback to coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude)
          .timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              debugPrint('⏰ LocationService: Reverse geocoding timed out after 5s - using coordinates');
              throw TimeoutException('Reverse geocoding timeout', const Duration(seconds: 5));
            },
          );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        // Try to get city, locality, or administrative area
        final cityName = placemark.locality ??
                        placemark.subAdministrativeArea ??
                        placemark.administrativeArea ??
                        placemark.country ??
                        'Unknown Location';

        debugPrint('✅ LocationService: Reverse geocoding successful: $cityName');
        return cityName;
      }

      debugPrint('⚠️ LocationService: No placemarks found, using coordinates as fallback');
      return _formatCoordinatesAsFallback(latitude, longitude);

    } catch (e) {
      final errorString = e.toString();
      debugPrint('❌ LocationService: Reverse geocoding failed: $e');

      // Handle specific error types
      if (errorString.contains('gcoo: UNAVAILABLE')) {
        debugPrint('🚫 LocationService: Geocoding service unavailable - using coordinates directly');
      } else if (errorString.contains('NETWORK_ERROR') || errorString.contains('IO_ERROR')) {
        debugPrint('🌐 LocationService: Network error during reverse geocoding - using coordinates');
      } else if (errorString.contains('TimeoutException') || errorString.contains('timeout')) {
        debugPrint('⏰ LocationService: Reverse geocoding timeout - using coordinates');
      } else {
        debugPrint('❓ LocationService: Unknown reverse geocoding error - using coordinates');
      }

      // Always fallback to coordinates when reverse geocoding fails
      debugPrint('🔄 LocationService: Using coordinates as search term fallback');
      return _formatCoordinatesAsFallback(latitude, longitude);
    }
  }

  /// Format coordinates as fallback search term
  static String _formatCoordinatesAsFallback(double latitude, double longitude) {
    // Format coordinates to 6 decimal places for API
    final lat = latitude.toStringAsFixed(6);
    final lng = longitude.toStringAsFixed(6);
    debugPrint('📍 LocationService: Using coordinates as search term: $lat,$lng');
    return '$lat,$lng';
  }

  /// Cache API location result
  static Future<void> _cacheApiLocation(ApiLocationResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (result.selectedLocation != null) {
        final locationData = {
          'selectedLocation': result.selectedLocation!.toJson(),
          'displayName': result.displayName,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        // Use json.encode instead of toString() for proper JSON serialization
        await prefs.setString(_locationCacheKey, json.encode(locationData));
        await prefs.setInt(_locationCacheTimeKey, DateTime.now().millisecondsSinceEpoch);
        debugPrint('📦 LocationService: Cached location data');
      }
    } catch (e) {
      debugPrint('❌ LocationService: Error caching API location: $e');
    }
  }

  /// Get cached API location result
  static Future<ApiLocationResult?> _getCachedApiLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheTime = prefs.getInt(_locationCacheTimeKey);

      if (cacheTime == null) {
        debugPrint('📦 LocationService: No cache time found');
        return null;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final cacheAge = Duration(milliseconds: now - cacheTime);

      if (cacheAge.inMinutes > _cacheValidityMinutes) {
        debugPrint('📦 LocationService: Cache expired (${cacheAge.inMinutes} minutes old)');
        return null; // Cache expired
      }

      final cachedData = prefs.getString(_locationCacheKey);
      if (cachedData != null) {
        debugPrint('📦 LocationService: Found cached data, parsing...');

        // Parse the cached JSON data
        final Map<String, dynamic> locationData = json.decode(cachedData);
        final selectedLocationJson = locationData['selectedLocation'] as Map<String, dynamic>;
        final selectedLocation = LocationSuggestion.fromJson(selectedLocationJson);

        final result = ApiLocationResult.success(
          selectedLocation: selectedLocation,
          isFromCache: true,
        );

        debugPrint('✅ LocationService: Successfully parsed cached location: ${selectedLocation.name}');
        return result;
      }

      debugPrint('📦 LocationService: No cached data found');
      return null;
    } catch (e) {
      debugPrint('❌ LocationService: Error getting cached API location: $e');
      return null;
    }
  }

  /// Utility methods for backward compatibility (if needed)

  /// Check if location permission is available
  static Future<bool> hasLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    return permission == LocationPermission.always ||
           permission == LocationPermission.whileInUse;
  }

  /// Open device location settings
  static Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      debugPrint('Error opening location settings: $e');
      return false;
    }
  }

  /// Open app settings for permissions
  static Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  /// Calculate distance between two points in kilometers
  static double calculateDistance(
    double lat1, double lon1,
    double lat2, double lon2,
  ) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2) / 1000; // Convert to km
  }

  /// DEPRECATED: Use getCurrentLocationWithAPI() instead
  /// Kept for backward compatibility only
  static Future<LocationResult> getCurrentLocation({
    bool useCache = true,
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    // For backward compatibility, convert API result to old format
    final apiResult = await getCurrentLocationWithAPI(useCache: useCache);

    if (apiResult.isSuccess && apiResult.selectedLocation != null) {
      return LocationResult.success(
        latitude: apiResult.selectedLocation!.coordinates.latitude,
        longitude: apiResult.selectedLocation!.coordinates.longitude,
        locationName: apiResult.displayName ?? 'Unknown Location',
        accuracy: 0.0,
        timestamp: DateTime.now(),
        isFromCache: apiResult.isFromCache,
      );
    } else {
      return LocationResult.error(
        apiResult.error ?? 'Failed to get location',
        LocationErrorType.unknown,
      );
    }
  }
}

/// DEPRECATED: Location result class for backward compatibility
/// Use ApiLocationResult instead
class LocationResult {
  final double latitude;
  final double longitude;
  final String locationName;
  final double accuracy;
  final DateTime timestamp;
  final bool isSuccess;
  final String? error;
  final LocationErrorType? errorType;
  final bool isFromCache;

  const LocationResult({
    required this.latitude,
    required this.longitude,
    required this.locationName,
    required this.accuracy,
    required this.timestamp,
    required this.isSuccess,
    this.error,
    this.errorType,
    this.isFromCache = false,
  });

  factory LocationResult.success({
    required double latitude,
    required double longitude,
    required String locationName,
    required double accuracy,
    required DateTime timestamp,
    bool isFromCache = false,
  }) {
    return LocationResult(
      latitude: latitude,
      longitude: longitude,
      locationName: locationName,
      accuracy: accuracy,
      timestamp: timestamp,
      isSuccess: true,
      isFromCache: isFromCache,
    );
  }

  factory LocationResult.error(String error, LocationErrorType errorType) {
    return LocationResult(
      latitude: 0.0,
      longitude: 0.0,
      locationName: '',
      accuracy: 0.0,
      timestamp: DateTime.now(),
      isSuccess: false,
      error: error,
      errorType: errorType,
    );
  }

  LocationResult copyWith({
    double? latitude,
    double? longitude,
    String? locationName,
    double? accuracy,
    DateTime? timestamp,
    bool? isSuccess,
    String? error,
    LocationErrorType? errorType,
    bool? isFromCache,
  }) {
    return LocationResult(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      accuracy: accuracy ?? this.accuracy,
      timestamp: timestamp ?? this.timestamp,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error ?? this.error,
      errorType: errorType ?? this.errorType,
      isFromCache: isFromCache ?? this.isFromCache,
    );
  }
}
