import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../core/constants/api_constants.dart';
import '../../../../core/error/exceptions.dart';
import '../models/search_cities.dart';
import '../models/api_location_models.dart';

abstract class SearchRemoteDataSource {
  Future<List<SearchCities>> searchCities(String query);

  /// New API methods for location integration
  Future<AutosuggestResponse> autoSuggestLocation(String term);

  /// Fire-and-forget call to /location API
  /// Used in BOTH scenarios:
  /// 1. Current location: After auto-selecting first item from autosuggest
  /// 2. Manual search: When user manually selects from autosuggest dropdown
  /// Only verifies 200 OK status, response body is disregarded
  Future<bool> fireAndForgetLocation(String locationId, LocationCoordinates coordinates);
}

class SearchRemoteDataSourceImpl implements SearchRemoteDataSource {
  final http.Client client;

  SearchRemoteDataSourceImpl({required this.client});

  @override
  Future<List<SearchCities>> searchCities(String query) async {
    try {
      final response = await client.get(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.autoSuggest}?q=$query'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData.map((e) => SearchCities.fromJson(e)).toList();
      } else {
        throw ServerException('Failed to search cities: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<AutosuggestResponse> autoSuggestLocation(String term) async {
    try {
      final url = '${ApiConstants.fullBaseUrl}${ApiConstants.autoSuggest}?term=${Uri.encodeComponent(term)}';
      print('🌐 API Call: $url');

      final response = await client.get(
        Uri.parse(url),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      print('📡 API Response: Status ${response.statusCode}');
      print('📄 API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final result = AutosuggestResponse.fromJson(jsonData);
        print('✅ API Success: ${result.locationSuggestions.length} suggestions');
        return result;
      } else {
        print('❌ API Error: Status ${response.statusCode}');
        return AutosuggestResponse.error('Failed to get location suggestions: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ API Exception: $e');
      if (e is ServerException) rethrow;
      return AutosuggestResponse.error('Network error occurred: $e');
    }
  }

  @override
  Future<bool> fireAndForgetLocation(String locationId, LocationCoordinates coordinates) async {
    try {
      final url = '${ApiConstants.fullBaseUrl}/location';
      final body = {
        'id': locationId,
        'coordinates': coordinates.toJson(),
      };
      print('🔥 Fire-and-forget call: $url');
      print('📤 Fire-and-forget body: ${json.encode(body)}');

      final response = await client.post(
        Uri.parse(url),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
        body: json.encode(body),
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      print('📡 Fire-and-forget response: ${response.statusCode}');

      // Fire-and-forget: only check for 200 OK status
      final success = response.statusCode == 200;
      print('✅ Fire-and-forget result: $success');
      return success;
    } catch (e) {
      print('❌ Fire-and-forget error: $e');
      // Fire-and-forget: return false on any error but don't throw
      return false;
    }
  }
}