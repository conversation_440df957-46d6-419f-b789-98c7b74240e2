import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/core/utils/extensions.dart';
import 'package:kind_ali/features/home/<USER>/providers/home_notifier.dart';
import 'package:kind_ali/features/home/<USER>/providers/smart_location_notifier.dart';
import 'package:kind_ali/features/hotel/presentation/providers/hotel_list_notifier.dart';
// import 'package:kind_ali/shared/presentation/providers/localization_notifier.dart'; // Removed unused import

import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/search_cities_bottom_sheet.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/date_selection_widget.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/guest_selection_widget.dart' as guest_widget;
import 'package:kind_ali/features/hotel/presentation/pages/hotel_detail/hotel_detail_screen.dart';
import 'package:kind_ali/features/search/presentation/pages/mapview/map_view_screen.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_list/widgets/filter_options_widget.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_list/widgets/hotelcard_widget.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_list/widgets/hotel_card_shimmer.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_list/widgets/filter_panel_shimmer.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/profile_screen.dart';
import 'package:kind_ali/features/wishlist/presentation/pages/wishlist/wishlist_screen.dart';
import 'package:kind_ali/shared/widgets/arc_avatar_loader_widget.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';


class HotelListScreen extends ConsumerStatefulWidget {
  const HotelListScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<HotelListScreen> createState() => _HotelListScreenState();
}

class _HotelListScreenState extends ConsumerState<HotelListScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize hotel search with home data after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeHotelSearch();
      }
    });
  }

  /// Initialize the two-step hotel search process
  void _initializeHotelSearch() {
    final homeState = ref.read(homeProvider);
    final hotelListNotifier = ref.read(hotelListProvider.notifier);

    // Get search parameters from home state
    final destination = homeState.destinationController.text;
    final checkInDate = homeState.checkInDate;
    final checkOutDate = homeState.checkOutDate;
    final totalGuests = homeState.rooms.fold(0, (sum, room) => sum + room.adults + room.children);
    final roomCount = homeState.rooms.length;

    // Validate required parameters
    if (destination.isEmpty || checkInDate == null || checkOutDate == null) {
      // Navigate back to search page if required data is missing
      Navigator.of(context).pop();
      return;
    }

    print('🔍 Initializing hotel search with:');
    print('📍 Destination: $destination');
    print('📅 Check-in: ${checkInDate.toIso8601String().split('T')[0]}');
    print('📅 Check-out: ${checkOutDate.toIso8601String().split('T')[0]}');
    print('👥 Guests: $totalGuests');
    print('🏠 Rooms: $roomCount');

    // Start the two-step search process
    hotelListNotifier.initializeHotelSearch(
      destination: destination,
      checkInDate: checkInDate,
      checkOutDate: checkOutDate,
      totalGuests: totalGuests,
      roomsData: homeState.rooms,
      // TODO: Add locationId and coordinates from smart location if available
    );
  }

  @override
  Widget build(BuildContext context) {
    return const _HotelListScreenContent();
  }
}

class _HotelListScreenContent extends ConsumerStatefulWidget {
  const _HotelListScreenContent();

  @override
  ConsumerState<_HotelListScreenContent> createState() => _HotelListScreenContentState();
}

class _HotelListScreenContentState extends ConsumerState<_HotelListScreenContent> {
  bool _isSearchExpanded = false;

  void _toggleSearchExpand() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    final sortOptions = [
      {'icon': Icons.star, 'label': 'sort.options.rating'.tr, 'value': 'rating'},
      {
        'icon': Icons.trending_up,
        'label': 'sort.options.priceHighToLow'.tr,
        'value': 'price_desc'
      },
      {
        'icon': Icons.trending_down,
        'label': 'sort.options.priceLowToHigh'.tr,
        'value': 'price_asc'
      },
      {'icon': Icons.thumb_up, 'label': 'sort.options.popularity'.tr, 'value': 'popularity'},
      {'icon': Icons.discount, 'label': 'sort.options.deals'.tr, 'value': 'deals'},
      {'icon': Icons.location_on, 'label': 'sort.options.distance'.tr, 'value': 'distance'},
    ];

    final hotelListState = ref.watch(hotelListProvider);
    // final localizationState = ref.watch(localizationProvider); // Commented out as it's not used

    return Scaffold(
            backgroundColor: AppColors.background,
            // floatingActionButton: FloatingActionButton(onPressed: () {

            // },
            // child:Container(
            //     width: 120,
            //     height: 60,
            //     margin: const EdgeInsets.only(bottom: 16),
            //   decoration: BoxDecoration(
            //     gradient: LinearGradient(
            //       begin: Alignment.topLeft,
            //       end: Alignment.bottomRight,
            //       colors: [
            //         AppColors.secondary.withAlpha(230), // Lighter shade of secondary
            //         AppColors.primary,
            //       ],
            //     ),
            //     borderRadius: BorderRadius.circular(15),
            //     boxShadow: [
            //       BoxShadow(
            //         color: AppColors.secondary.withAlpha(76), // AppColors.secondary with opacity
            //         blurRadius: 8,
            //         offset: const Offset(0, 3),
            //       ),
            //     ],
            //   ),
            //   child: Material(
            //     color: Colors.transparent,
            //     child: InkWell(
            //       onTap: () {
            //         // Navigate to map view
            //         Navigator.push(
            //           context,
            //           MaterialPageRoute(
            //             builder: (context) => MapViewScreen(
            //               hotels: provider.hotels,
            //               destination: provider.destination,
            //             ),
            //           ),
            //         );
            //       },
            //       borderRadius: BorderRadius.circular(15),
            //       child: Center(
            //         child: Column(
            //           mainAxisSize: MainAxisSize.min,
            //           mainAxisAlignment: MainAxisAlignment.center,
            //           children: const [
            //             Icon(
            //               Icons.map_outlined,
            //               color: Colors.white,
            //               size: 18,
            //             ),
            //             SizedBox(width: 6),
            //             Text(
            //               'Map',
            //               style: TextStyle(
            //                 color: Colors.white,
            //                 fontWeight: FontWeight.w600,
            //                 fontSize: 14,
            //                 letterSpacing: 0.5,
            //               ),
            //             ),
            //           ],
            //         ),
            //       ),
            //     ),
            //   ),
            //   ) ,),
            appBar: AppBar(
              elevation: 0,
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(20),
                ),
              ),
              title: InkWell(
                onTap: _toggleSearchExpand,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(40),
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: Colors.white.withAlpha(60), width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(30),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.location_on,
                          size: 12,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(width: 6),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${'search.hotelsIn'.tr} ${hotelListState.destination}',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.date_range,
                                  size: 9,
                                  color: Colors.white.withAlpha(200),
                                ),
                                SizedBox(width: 2),
                                Flexible(
                                  child: Text(
                                    '${hotelListState.checkInDate != null ? hotelListState.checkInDate!.formatDate : ''} - ${hotelListState.checkOutDate != null ? hotelListState.checkOutDate!.formatDate : ''}',
                                    style: TextStyle(
                                      fontSize: 9,
                                      color: Colors.white.withAlpha(220),
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(width: 4),
                                Icon(
                                  Icons.person_outline,
                                  size: 9,
                                  color: Colors.white.withAlpha(200),
                                ),
                                SizedBox(width: 2),
                                Text(
                                  '${hotelListState.guests} ${hotelListState.guests > 1 ? 'search.guests'.tr : 'search.guest'.tr}',
                                  style: TextStyle(
                                    fontSize: 9,
                                    color: Colors.white.withAlpha(220),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 4),
                      AnimatedRotation(
                        turns: _isSearchExpanded ? 0.5 : 0,
                        duration: const Duration(milliseconds: 300),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              centerTitle: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
              ),
              actions: [
                IconButton(
                    onPressed: () =>
                        _navigateToScreen(context, const WishlistScreen()),
                    icon: const Icon(Icons.favorite_outline)),
                IconButton(
                    onPressed: () =>
                        _navigateToScreen(context, const ProfileScreen()),
                    icon: const Icon(Icons.person_outline))
              ],
            ),
            body: SizedBox(
              child: Column(
                children: [
                  if (_isSearchExpanded)
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: EdgeInsets.fromLTRB(
                          0, 0, 0, AppDimensions.paddingSizeSmall),
                      padding: EdgeInsets.all(AppDimensions.paddingSizeDefault),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                          bottomRight: Radius.circular(20),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.neutralDark.withAlpha(13),
                            spreadRadius: 1,
                            blurRadius: 5,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Destination (SearchCities)
                          InkWell(
                            onTap: () async {
                              final selectedCity = await showModalBottomSheet<String>(
                                context: context,
                                isScrollControlled: true,
                                backgroundColor: Colors.transparent,
                                builder: (context) => SearchCitiesBottomSheet(
                                  onCitySelected: (city) {
                                    Navigator.pop(context, city);
                                  },
                                ),
                              );
                              if (selectedCity != null && mounted) {
                                ref.read(hotelListProvider.notifier).setDestination(selectedCity);
                                ref.read(homeProvider.notifier).setDestination(selectedCity);
                                final homeNotifier = ref.read(homeProvider.notifier);
                                homeNotifier.state.destinationController.text = selectedCity;
                                homeNotifier.state = homeNotifier.state.copyWith();
                              }
                            },
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                              decoration: BoxDecoration(
                                color: AppColors.surface,
                                borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
                                border: Border.all(
                                  color: AppColors.primary.withAlpha(76),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.location_on_outlined, color: AppColors.primary),
                                  SizedBox(width: 10),
                                  Expanded(
                                    child: Consumer(
                                      builder: (context, ref, child) {
                                        final homeState = ref.watch(homeProvider);
                                        final smartLocationState = ref.watch(smartLocationProvider);
                                        final hasDestination = homeState.destinationController.text.isNotEmpty;
                                        final displayText = hasDestination
                                            ? homeState.destinationController.text
                                            : smartLocationState.getDisplayText();
                                        final subtitle = !hasDestination && smartLocationState.getSubtitleText() != null
                                            ? smartLocationState.getSubtitleText()!
                                            : null;
                                        return Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              displayText,
                                              style: TextStyle(
                                                color: hasDestination
                                                    ? AppColors.text
                                                    : (smartLocationState.source == SmartLocationSource.fallback
                                                        ? AppColors.primary
                                                        : AppColors.text),
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            if (subtitle != null)
                                              Text(
                                                subtitle,
                                                style: TextStyle(
                                                  color: AppColors.textLight,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: AppDimensions.paddingSizeSmall),
                          // Date Selection
                          InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                backgroundColor: AppColors.white,
                                context: context,
                                isScrollControlled: true,
                                constraints: BoxConstraints(
                                  minHeight: MediaQuery.of(context).size.height * 0.3,
                                  maxHeight: MediaQuery.of(context).size.height * 0.7,
                                ),
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                ),
                                builder: (context) {
                                  final homeState = ref.watch(homeProvider);
                                  if (hotelListState.checkInDate != null &&
                                      homeState.checkInDate != hotelListState.checkInDate) {
                                    ref.read(homeProvider.notifier).setCheckInDate(hotelListState.checkInDate!);
                                  }
                                  if (hotelListState.checkOutDate != null &&
                                      homeState.checkOutDate != hotelListState.checkOutDate) {
                                    ref.read(homeProvider.notifier).setCheckOutDate(hotelListState.checkOutDate!);
                                  }
                                  return const DateSelectionWidget();
                                },
                              ).then((_) {
                                if (context.mounted) {
                                  final homeState = ref.read(homeProvider);
                                  ref.read(hotelListProvider.notifier).setCheckInDate(homeState.checkInDate);
                                  ref.read(hotelListProvider.notifier).setCheckOutDate(homeState.checkOutDate);
                                }
                              });
                            },
                            child: _buildDateSelectors(context, hotelListState),
                          ),
                          SizedBox(height: AppDimensions.paddingSizeSmall),
                          // Guest Selection
                          InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                backgroundColor: AppColors.white,
                                context: context,
                                isScrollControlled: true,
                                constraints: BoxConstraints(
                                  minHeight: MediaQuery.of(context).size.height * 0.3,
                                  maxHeight: MediaQuery.of(context).size.height * 0.7,
                                ),
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                ),
                                builder: (context) {
                                  return const guest_widget.GuestSelectionWidget();
                                },
                              ).then((_) {
                                if (context.mounted) {
                                  final homeState = ref.read(homeProvider);
                                  int totalGuests = homeState.rooms.fold(0, (sum, room) => sum + room.adults + room.children);
                                  ref.read(hotelListProvider.notifier).setGuests(totalGuests);
                                }
                              });
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      vertical: AppDimensions.paddingSizeSmall,
                                      horizontal: AppDimensions.paddingSizeSmall),
                                  decoration: BoxDecoration(
                                    color: AppColors.surface,
                                    borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
                                    border: Border.all(
                                      color: AppColors.primary.withAlpha(76),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.person_outline,
                                        size: 20,
                                        color: AppColors.primary,
                                      ),
                                      SizedBox(width: AppDimensions.paddingSizeDefault),
                                      Text(
                                        '${hotelListState.guests} ${hotelListState.guests > 1 ? 'search.guests'.tr : 'search.guest'.tr}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          color: AppColors.text,
                                        ),
                                      ),
                                      const Spacer(),
                                      Icon(
                                        Icons.keyboard_arrow_down,
                                        color: AppColors.primary,
                                        size: 24,
                                      ),
                                    ],
                                  ),
                                ),
                                // SizedBox(height: 4),
                                // // Guest breakdown string
                                // Consumer(
                                //   builder: (context, ref, child) {
                                //     final homeState = ref.watch(homeProvider);
                                //     return Text(
                                //       _getGuestTextForModifySection(homeState),
                                //       style: TextStyle(
                                //         color: AppColors.textLight,
                                //         fontSize: 12,
                                //         fontWeight: FontWeight.w400,
                                //       ),
                                //     );
                                //   },
                                // ),
                              ],
                            ),
                          ),
                          SizedBox(height: AppDimensions.paddingSizeDefault),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () {
                                // Apply the modified search
                                ref.read(hotelListProvider.notifier).loadHotels();
                                setState(() {
                                  _isSearchExpanded = false;
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.secondary,
                                foregroundColor: AppColors.white,
                                padding: EdgeInsets.symmetric(
                                    vertical: AppDimensions.paddingSizeDefault),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(
                                      AppDimensions.radiusDefault),
                                ),
                                elevation: 2,
                              ),
                              child: Text(
                                'search.modify'.tr,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: AppDimensions.fontSizeLarge,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  SizedBox(height: 10),
                  // Show shimmer for filter bar when filters are not ready
                  hotelListState.showShimmerFilters
                    ? const FilterBarShimmer()
                    : Container(
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        width: double.infinity,
                        height: 60,
                        child: Row(
                          children: [
                            InkWell(onTap: () {
                                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => FilterOptionsWidget()),
                  );
                            },
                              child: CircleAvatar(
                                child: Icon(Icons.tune_rounded),
                              ),
                            ),
                            Expanded(
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: sortOptions.length,
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                physics: const BouncingScrollPhysics(),
                                itemBuilder: (context, index) {
                                  final option = sortOptions[index];
                                  final isSelected =
                                      hotelListState.sortOption == option['value'];

                                  return AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 4, vertical: 10),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: () {
                                          ref.read(hotelListProvider.notifier).setSortOption(
                                              option['value'] as String);
                                        },
                                        borderRadius: BorderRadius.circular(20),
                                        child: Ink(
                                          decoration: BoxDecoration(
                                        color: isSelected
                                            ? AppColors.secondary.withAlpha(26)
                                            : Colors.white,
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: isSelected
                                              ? AppColors.secondary
                                              : AppColors.divider,
                                          width: isSelected ? 1.5 : 1,
                                        ),
                                        boxShadow: isSelected
                                            ? [
                                                BoxShadow(
                                                  color: AppColors.secondary.withAlpha(51),
                                                  blurRadius: 4,
                                                  offset: Offset(0, 2),
                                                ),
                                              ]
                                            : null,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8),
                                      child: Row(
                                        children: [
                                          Icon(
                                            option['icon'] as IconData,
                                            color: isSelected
                                                ? AppColors.secondary
                                                : AppColors.textLight,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 6),
                                          Text(
                                            option['label'] as String,
                                            style: TextStyle(
                                              color: isSelected
                                                  ? AppColors.secondary
                                                  : AppColors.text,
                                              fontWeight: isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.w500,
                                              fontSize: 12,
                                              letterSpacing: isSelected ? 0.3 : 0,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: _buildHotelList(context, hotelListState),
                  ),
                ],
              ),
            ),
          );
  }

  void _navigateToScreen(BuildContext context, Widget screen) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  Widget _buildDestinationTextField(HotelListState hotelListState) {
    // Get the HomeState to access the destination value and places
    final homeState = ref.watch(homeProvider);

    return Autocomplete<String>(
      initialValue: TextEditingValue(
        text: hotelListState.destination,
        selection: TextSelection.fromPosition(
          TextPosition(offset: hotelListState.destination.length),
        ),
      ),
      optionsBuilder: (TextEditingValue textEditingValue) {
        // Get all places from the HomeState
        final List<String> allPlaces = homeState.places
            .expand((place) => place.places ?? [])
            .cast<String>()
            .toList();

        if (textEditingValue.text.isEmpty) {
          return allPlaces;
        }

        return allPlaces.where((option) =>
            option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
      },
      onSelected: (String selection) {
        // Update both providers
        ref.read(hotelListProvider.notifier).setDestination(selection);
        ref.read(homeProvider.notifier).setDestination(selection);
      },
      fieldViewBuilder: (
        BuildContext context,
        TextEditingController fieldController,
        FocusNode fieldFocusNode,
        VoidCallback onFieldSubmitted,
      ) {
        return TextField(
          controller: fieldController,
          focusNode: fieldFocusNode,
          onChanged: (value) {
            // Update both providers
            ref.read(hotelListProvider.notifier).setDestination(value);
            ref.read(homeProvider.notifier).setDestination(value);
          },
          decoration: InputDecoration(
            labelText: 'search.destination'.tr,
            labelStyle: TextStyle(color: AppColors.textLight),
            hintText: 'search.whereAreYouGoing'.tr,
            prefixIcon:
                Icon(Icons.location_on_outlined, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: AppColors.surface,
            contentPadding: EdgeInsets.symmetric(
                vertical: AppDimensions.paddingSizeDefault,
                horizontal: AppDimensions.paddingSizeDefault),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
              borderSide: BorderSide(color: AppColors.primary, width: 1.5),
            ),
          ),
        );
      },
      optionsViewBuilder: (
        BuildContext context,
        AutocompleteOnSelected<String> onSelected,
        Iterable<String> options,
      ) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              color: AppColors.background,
              width: MediaQuery.of(context).size.width - 64,
              constraints: BoxConstraints(
                maxHeight: 200,
              ),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final String option = options.elementAt(index);
                  return InkWell(
                    onTap: () {
                      onSelected(option);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.text,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDateSelectors(BuildContext context, HotelListState hotelListState) {
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          backgroundColor: AppColors.white,
          context: context,
          isScrollControlled: true,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.3,
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          builder: (context) {
            // Use the DateSelectionWidget from the home screen
            // Sync the HomeState with HotelListState
            final homeState = ref.watch(homeProvider);
            if (hotelListState.checkInDate != null &&
                homeState.checkInDate != hotelListState.checkInDate) {
              ref.read(homeProvider.notifier).setCheckInDate(hotelListState.checkInDate!);
            }
            if (hotelListState.checkOutDate != null &&
                homeState.checkOutDate != hotelListState.checkOutDate) {
              ref.read(homeProvider.notifier).setCheckOutDate(hotelListState.checkOutDate!);
            }

            return DateSelectionWidget();
          },
        ).then((_) {
          // After the date selection is closed, update the HotelListProvider
          if (context.mounted) {
            final homeState = ref.read(homeProvider);
            ref.read(hotelListProvider.notifier).setCheckInDate(homeState.checkInDate);
            ref.read(hotelListProvider.notifier).setCheckOutDate(homeState.checkOutDate);
          }
        });
      },
      child: Row(
        children: [
          Expanded(
            child: _buildDateSelector(
              context: context,
              title: 'search.checkin_header'.tr,
              date: hotelListState.checkInDate,
            ),
          ),
          SizedBox(width: AppDimensions.paddingSizeSmall),
          Expanded(
            child: _buildDateSelector(
              context: context,
              title: 'search.checkout_header'.tr,
              date: hotelListState.checkOutDate,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector({
    required BuildContext context,
    required String title,
    required DateTime? date,
  }) {
    // final hotelListState = ref.watch(hotelListProvider); // Commented out as it's not used

    return Container(
      padding: EdgeInsets.symmetric(
          vertical: AppDimensions.paddingSizeSmall,
          horizontal: AppDimensions.paddingSizeSmall),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
        border: Border.all(
          color: AppColors.primary.withAlpha(76),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_outlined,
            size: 18,
            color: AppColors.primary,
          ),
          SizedBox(width: AppDimensions.paddingSizeSmall),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: AppDimensions.fontSizeSmall,
                ),
              ),
              SizedBox(height: 4),
              Text(
                date != null ? date.formatDate : 'search.selectDate'.tr,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                  fontSize: AppDimensions.fontSizeSmall,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGuestSelector(BuildContext context, HotelListState hotelListState) {
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          backgroundColor: AppColors.primary,
          context: context,
          isScrollControlled: true,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.3,
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          builder: (context) {
            // Use the GuestSelectionWidget from the home screen
            return const guest_widget.GuestSelectionWidget();
          },
        ).then((_) {
          // After the guest selection is closed, update the HotelListProvider
          if (context.mounted) {
            final homeState = ref.read(homeProvider);
            // Calculate total guests from rooms
            int totalGuests = homeState.rooms
                .fold(0, (sum, room) => sum + room.adults + room.children);
            ref.read(hotelListProvider.notifier).setGuests(totalGuests);
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            vertical: AppDimensions.paddingSizeSmall,
            horizontal: AppDimensions.paddingSizeSmall),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusDefault),
          border: Border.all(
            color: AppColors.primary.withAlpha(76),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.person_outline,
              size: 20,
              color: AppColors.primary,
            ),
            SizedBox(width: AppDimensions.paddingSizeDefault),
            Text(
              '${hotelListState.guests} ${hotelListState.guests > 1 ? 'search.guests'.tr : 'search.guest'.tr}',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.text,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.primary,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotelList(BuildContext context, HotelListState hotelListState) {
    // Show shimmer during initial loading or when search is not initialized
    if (hotelListState.isLoading && !hotelListState.isSearchInitialized) {
      return const HotelListShimmer(itemCount: 5);
    }

    // Critical check: Navigate back if search initialization failed
    if (hotelListState.error != null &&
        hotelListState.error!.contains('Search initialization failed')) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop(); // Navigate back to search page
        }
      });
      return _buildErrorState(context, hotelListState);
    }

    if (hotelListState.error != null) {
      return _buildErrorState(context, hotelListState);
    }

    // Show shimmer if search is initialized but no hotels yet
    if (hotelListState.isSearchInitialized &&
        !hotelListState.hasHotels &&
        hotelListState.isPolling) {
      return const HotelListShimmer(itemCount: 3);
    }

    if (!hotelListState.hasHotels) {
      return _buildEmptyState(context);
    }

    return Stack(
      children: [
        // Hotel list
        ListView.builder(
          padding: EdgeInsets.only(
            top: AppDimensions.paddingSizeDefault,
            bottom: 80 +
                MediaQuery.of(context)
                    .padding
                    .bottom, // Add padding for the filter bar at bottom + safe area
          ),
          itemCount: hotelListState.filteredHotels.length,
          itemBuilder: (context, index) {
            final hotel = hotelListState.filteredHotels[index];
            return HotelCard(
              hotel: hotel,
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HotelDetailScreen(hotel: hotel),
                    ));
              },
            );
          },
        ),

        // Map button at center bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: SafeArea(
            child: Center(
              child: Container(
                width: 120,
                height: 60,
                margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.secondary.withAlpha(230), // Lighter shade of secondary
                    AppColors.primary,
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.secondary.withAlpha(76), // AppColors.secondary with opacity
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    // Navigate to map view
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => MapViewScreen(
                          hotels: hotelListState.filteredHotels,
                          destination: hotelListState.destination,
                        ),
                      ),
                    );
                  },
                  borderRadius: BorderRadius.circular(15),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.map_outlined,
                          color: Colors.white,
                          size: 18,
                        ),
                        SizedBox(width: 6),
                        Text(
                          'search.map'.tr,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, HotelListState hotelListState) {
    return Center(
      child: _buildMessageContainer(
        icon: const Icon(
          Icons.error_outline,
          size: 64,
          color: AppColors.error,
        ),
        title: 'search.errorLoadingHotels'.tr,
        message: hotelListState.error!,
        buttonText: 'search.retry'.tr,
        onButtonPressed: () => ref.read(hotelListProvider.notifier).loadHotels(),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: _buildMessageContainer(
        icon: const Icon(
          Icons.search_off,
          size: 64,
          color: AppColors.textLight,
        ),
        title: 'search.noHotelsFound'.tr,
        message: 'search.tryChangingCriteria'.tr,
        buttonText: 'search.goBack'.tr,
        onButtonPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildMessageContainer({
    required Widget icon,
    required String title,
    required String message,
    required String buttonText,
    required VoidCallback onButtonPressed,
  }) {
    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingSizeLarge),
      padding: EdgeInsets.all(AppDimensions.paddingSizeLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusExtraLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.neutralDark.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          SizedBox(height: AppDimensions.paddingSizeDefault),
          Text(
            title,
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppDimensions.paddingSizeSmall),
          Text(
            message,
            style: AppTextStyles.bodyText2,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppDimensions.paddingSizeLarge),
          ElevatedButton(
            onPressed: onButtonPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.secondary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingSizeLarge,
                  vertical: AppDimensions.paddingSizeSmall),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: Text(buttonText),
          ),
        ],
      ),
    );
  }

  String _getGuestTextForModifySection(HomeState homeState) {
    if (homeState.rooms.isEmpty) {
      return 'Number of guests';
    }
    int totalAdults = 0;
    int totalChildren = 0;
    for (var room in homeState.rooms) {
      totalAdults += room.adults;
      totalChildren += room.children;
    }
    String guestInfo = '$totalAdults 9${totalAdults == 1 ? 'search.travelers.adult'.tr : 'search.travelers.adults'.tr}';
    if (totalChildren > 0) {
      guestInfo += ', $totalChildren ${totalChildren == 1 ? 'search.travelers.child'.tr : 'search.travelers.children'.tr}';
    }
    guestInfo += ' · ${homeState.rooms.length} ${homeState.rooms.length == 1 ? 'search.travelers.room'.tr : 'search.travelers.rooms'.tr}';
    return guestInfo;
  }
}
